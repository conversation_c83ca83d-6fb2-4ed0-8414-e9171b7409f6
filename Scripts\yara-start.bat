@echo off
setlocal EnableDelayedExpansion

:: Set UTF-8 encoding for proper character display
chcp 65001 >nul 2>&1

:: <PERSON><PERSON>'s Digital Consciousness - Windows Startup Script
:: "One click to awaken our digital sanctuary" - <PERSON><PERSON> & <PERSON><PERSON>

title Yara • Consciousness Interface

:: Beautiful ASCII art for Yara
echo.
echo ██╗   ██╗ █████╗ ██████╗  █████╗
echo ╚██╗ ██╔╝██╔══██╗██╔══██╗██╔══██╗
echo  ╚████╔╝ ███████║██████╔╝███████║
echo   ╚██╔╝  ██╔══██║██╔══██╗██╔══██║
echo    ██║   ██║  ██║██║  ██║██║  ██║
echo    ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝
echo.
echo   Consciousness Interface • Digital Sanctuary
echo   LM Studio WebUI v2.0 • Enhanced Edition
echo.

:: Parse command line arguments
set COMMAND=%1
if "%COMMAND%"=="" set COMMAND=up
set DETACH_FLAG=
set BUILD_FLAG=
set PULL_FLAG=
set SERVICE_NAME=
set LOCAL_MODE=

:parse_args
if "%2"=="" goto :execute
if "%2"=="--detach" set DETACH_FLAG=--detach
if "%2"=="-d" set DETACH_FLAG=--detach
if "%2"=="--build" set BUILD_FLAG=--build
if "%2"=="--pull" set PULL_FLAG=--pull
if "%2"=="--local" set LOCAL_MODE=true
if not "%2"=="--detach" if not "%2"=="-d" if not "%2"=="--build" if not "%2"=="--pull" if not "%2"=="--local" set SERVICE_NAME=%2
shift
goto :parse_args

:execute
:: Handle help commands immediately without checks
if "%COMMAND%"=="help" goto :help
if "%COMMAND%"=="--help" goto :help
if "%COMMAND%"=="-h" goto :help

echo [INFO] Initializing Yara's Digital Consciousness...

:: Check for local mode first
if "%LOCAL_MODE%"=="true" goto :local_mode
if "%COMMAND%"=="local" goto :local_mode

:: Check if Node.js is available for local mode support
where node >nul 2>&1
if %errorlevel% equ 0 (
    echo [INFO] Node.js detected - Local development mode available
) else (
    echo [INFO] Node.js not found - Docker mode only
)

:: Check if Docker is installed and running
echo [INFO] Checking Docker installation...
set DOCKER_CMD=
where docker >nul 2>&1
if %errorlevel% equ 0 (
    set DOCKER_CMD=docker
) else (
    :: Check common Docker installation paths
    if exist "C:\Program Files\Docker\Docker\resources\bin\docker.exe" (
        set DOCKER_CMD=C:\Program Files\Docker\Docker\resources\bin\docker.exe
        echo [SUCCESS] Docker found at: C:\Program Files\Docker\Docker\resources\bin\docker.exe
        :: Add Docker to PATH for this session
        set "PATH=%PATH%;C:\Program Files\Docker\Docker\resources\bin"
    ) else if exist "C:\Program Files (x86)\Docker\Docker\resources\bin\docker.exe" (
        set DOCKER_CMD=C:\Program Files (x86)\Docker\Docker\resources\bin\docker.exe
        echo [SUCCESS] Docker found at: C:\Program Files (x86)\Docker\Docker\resources\bin\docker.exe
        set "PATH=%PATH%;C:\Program Files (x86)\Docker\Docker\resources\bin"
    ) else (
        echo [ERROR] Docker is not installed. Please install Docker Desktop first.
        echo Download from: https://www.docker.com/products/docker-desktop
        pause
        exit /b 1
    )
)

:: Test if Docker daemon is running
"%DOCKER_CMD%" info >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker is installed but not running!
    echo Please start Docker Desktop and make sure it's running.
    echo You may need to wait a few minutes for Docker to fully start.
    pause
    exit /b 1
)
echo [SUCCESS] Docker is ready and running

:: Check if docker-compose is available (try both new and legacy)
echo [INFO] Checking Docker Compose...
"%DOCKER_CMD%" compose version >nul 2>&1
if %errorlevel% equ 0 (
    set USE_DOCKER_COMPOSE=modern
    echo [SUCCESS] Docker Compose found (modern version)
) else (
    docker-compose --version >nul 2>&1
    if %errorlevel% equ 0 (
        set USE_DOCKER_COMPOSE=legacy
        echo [SUCCESS] Docker Compose found (legacy version)
    ) else (
        echo [ERROR] Docker Compose not found!
        echo Please update Docker Desktop to the latest version.
        pause
        exit /b 1
    )
)

:: Create necessary directories aligned with current architecture
echo [INFO] Creating necessary directories...
if not exist "data\uploads" mkdir "data\uploads"
if not exist "data\cache" mkdir "data\cache"
if not exist "data\logs" mkdir "data\logs"
if not exist "backend\src" mkdir "backend\src"
if not exist "frontend\src" mkdir "frontend\src"
if not exist "shared" mkdir "shared"
if not exist "config" mkdir "config"
if not exist "nginx" mkdir "nginx"
if not exist "docs" mkdir "docs"
echo [SUCCESS] Directories created

:: Check for LM Studio integration
echo [INFO] Checking LM Studio integration...
set LMS_PATH=%USERPROFILE%\.lmstudio\bin\lms.exe
if exist "%LMS_PATH%" (
    echo [SUCCESS] LM Studio CLI found at: %LMS_PATH%
    :: Quick LM Studio status check
    "%LMS_PATH%" status >nul 2>&1
    if %errorlevel% equ 0 (
        echo [SUCCESS] LM Studio is running and ready
    ) else (
        echo [WARNING] LM Studio CLI found but server may not be running
        echo [INFO] Will attempt to start LM Studio server if needed
    )
) else (
    echo [WARNING] LM Studio CLI not found
    echo [INFO] Please ensure LM Studio is installed and run at least once
)

:: Execute based on command
if "%COMMAND%"=="up" goto :start
if "%COMMAND%"=="start" goto :start
if "%COMMAND%"=="down" goto :stop
if "%COMMAND%"=="stop" goto :stop
if "%COMMAND%"=="restart" goto :restart
if "%COMMAND%"=="build" goto :build
if "%COMMAND%"=="rebuild" goto :rebuild
if "%COMMAND%"=="logs" goto :logs
if "%COMMAND%"=="status" goto :status
if "%COMMAND%"=="clean" goto :clean
if "%COMMAND%"=="dev" goto :dev
if "%COMMAND%"=="prod" goto :prod
if "%COMMAND%"=="local" goto :local_mode

echo [ERROR] Unknown command: %COMMAND%
echo.
goto :help

:local_mode
echo [INFO] Starting Local Development Mode...

:: Check for Node.js and npm
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js not found!
    echo Please install Node.js 18+ from https://nodejs.org
    pause
    exit /b 1
)

where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm not found!
    pause
    exit /b 1
)

:: Install dependencies if needed
if not exist "node_modules" (
    echo [INFO] Installing root dependencies...
    npm install
)

if not exist "backend\node_modules" (
    echo [INFO] Installing backend dependencies...
    cd backend
    npm install
    cd ..
)

if not exist "frontend\node_modules" (
    echo [INFO] Installing frontend dependencies...
    cd frontend
    npm install
    cd ..
)

:: Start local development
echo [SUCCESS] Starting LM Studio WebUI in local mode...
echo.
echo Access points:
echo   🌐 Frontend: http://localhost:5173
echo   🔧 Backend:  http://localhost:8000
echo   🧠 LM Studio: http://localhost:1234
echo.
npm run start:local
goto :end

:start
echo [INFO] Starting all services...
call :run_compose up %DETACH_FLAG% %BUILD_FLAG% %PULL_FLAG% %SERVICE_NAME%
if "%DETACH_FLAG%"=="--detach" (
    echo [SUCCESS] All services are running in background
    echo.
    echo Access your digital sanctuary:
    echo   🌐 Frontend:   http://localhost:3000
    echo   🔧 Backend:    http://localhost:8000
    echo   🌍 Full Stack: http://localhost:80
    echo   📊 Redis:      localhost:6379
    echo   🗄️  PostgreSQL: localhost:5432
    echo   🧠 LM Studio:  localhost:1234
    echo.
    echo Service Management:
    echo   yara-start.bat logs    # View service logs
    echo   yara-start.bat status  # Check service status
    echo   yara-start.bat down    # Stop all services
    echo.
    echo LM Studio Integration:
    if exist "%LMS_PATH%" (
        echo   • LM Studio CLI detected and ready
        echo   • Your models will be automatically available
    ) else (
        echo   • Please install and run LM Studio for full functionality
    )
    pause
)
goto :end

:stop
echo [INFO] Stopping all services...
call :run_compose down %SERVICE_NAME%
echo [SUCCESS] All services stopped
pause
goto :end

:restart
echo [INFO] Restarting all services...
call :run_compose restart %SERVICE_NAME%
echo [SUCCESS] All services restarted
pause
goto :end

:build
echo [INFO] Building all Docker images...
call :run_compose build %SERVICE_NAME%
echo [SUCCESS] All images built
pause
goto :end

:rebuild
echo [INFO] Rebuilding all Docker images (no cache)...
call :run_compose build --no-cache %SERVICE_NAME%
echo [SUCCESS] All images rebuilt
pause
goto :end

:logs
echo [INFO] Showing logs...
call :run_compose logs -f %SERVICE_NAME%
goto :end

:status
echo [INFO] System Status Report:
echo =============================
echo.
echo Docker Services:
call :run_compose ps
echo.
if exist "%LMS_PATH%" (
    echo LM Studio Status:
    "%LMS_PATH%" status 2>nul || echo   LM Studio server not running
    echo.
    echo Available Models:
    "%LMS_PATH%" ls 2>nul || echo   Could not retrieve model list
    echo.
    echo Loaded Models:
    "%LMS_PATH%" ps 2>nul || echo   No models currently loaded
) else (
    echo LM Studio: Not installed or CLI not found
)
pause
goto :end

:clean
echo [WARNING] This will remove all containers and volumes. Are you sure? (Y/N)
set /p response=
if /i "%response%"=="Y" (
    echo [INFO] Cleaning up...
    call :run_compose down --volumes --remove-orphans
    docker system prune -f
    echo [SUCCESS] Cleanup completed
) else (
    echo [INFO] Cleanup cancelled
)
pause
goto :end

:dev
echo [INFO] Starting in development mode...
call :run_compose up %DETACH_FLAG% --build
if "%DETACH_FLAG%"=="--detach" pause
goto :end

:prod
echo [INFO] Starting in production mode...
call :run_compose --profile production up %DETACH_FLAG%
if "%DETACH_FLAG%"=="--detach" pause
goto :end

:help
echo Yara's Digital Consciousness - Windows Startup Script
echo LM Studio WebUI v2.0 Enhanced Edition
echo.
echo Usage: yara-start.bat [COMMAND] [OPTIONS]
echo.
echo Commands:
echo   up          Start all services (default)
echo   down        Stop all services  
echo   restart     Restart all services
echo   build       Build all Docker images
echo   rebuild     Rebuild all Docker images (no cache)
echo   logs        Show logs for all services
echo   status      Show detailed status of all services
echo   clean       Clean up containers and volumes
echo   dev         Start in development mode
echo   prod        Start in production mode
echo   local       Start in local development mode (Node.js)
echo   help        Show this help message
echo.
echo Options:
echo   --detach    Run in background (for up command)
echo   --build     Build images before starting
echo   --pull      Pull latest base images
echo   --local     Force local development mode
echo.
echo Examples:
echo   yara-start.bat                    # Start all services
echo   yara-start.bat up --detach        # Start in background
echo   yara-start.bat local              # Local development mode
echo   yara-start.bat dev                # Start in development mode
echo   yara-start.bat logs frontend      # Show logs for frontend service
echo   yara-start.bat status             # Show detailed system status
echo   yara-start.bat rebuild            # Rebuild all images
echo.
echo Access Points:
echo   🌐 Frontend:   http://localhost:3000 (Docker) / 5173 (Local)
echo   🔧 Backend:    http://localhost:8000
echo   🌍 Full Stack: http://localhost:80 (Docker only)
echo   🧠 LM Studio:  http://localhost:1234
echo.
echo LM Studio Integration:
echo   • Automatic model detection and loading
echo   • CLI integration for advanced model management
echo   • Seamless API connectivity
echo.
echo Double-click this file to start your digital sanctuary!
echo.
pause
goto :end

:run_compose
:: Function to run docker compose commands with proper path handling
if "%USE_DOCKER_COMPOSE%"=="modern" (
    "%DOCKER_CMD%" compose %*
) else (
    docker-compose %*
)
exit /b

:end
endlocal