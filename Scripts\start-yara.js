#!/usr/bin/env node

import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import { fileURLToPath } from 'url';

const execAsync = promisify(exec);
const __dirname = path.dirname(fileURLToPath(import.meta.url));

class YaraStartup {
  constructor() {
    this.platform = process.platform;
    this.processes = [];
    this.lmstudioHost = process.env.LM_STUDIO_HOST || 'localhost';
    this.lmstudioPort = process.env.LM_STUDIO_PORT || '1234';
    this.backendPort = process.env.BACKEND_PORT || '8001';
    this.frontendPort = process.env.FRONTEND_PORT || '3000';
  }

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const icons = {
      info: '💙',
      success: '✅',
      warning: '⚠️',
      error: '❌',
      loading: '⏳'
    };
    console.log(`${icons[type]} [${timestamp}] ${message}`);
  }

  async findLMStudioCli() {
    const homeDir = process.env.HOME || process.env.USERPROFILE;
    const localAppData = process.env.LOCALAPPDATA || '';
    
    const cliPaths = this.platform === 'win32' 
      ? [
          path.join(homeDir, '.lmstudio', 'bin', 'lms.exe'),
          path.join(localAppData, 'Programs', 'LM Studio', 'resources', 'cli', 'lms.exe'),
          'lms.exe', 
          'lms'
        ]
      : [path.join(homeDir, '.lmstudio', 'bin', 'lms'), 'lms'];

    for (const cliPath of cliPaths) {
      try {
        const { stdout } = await execAsync(`"${cliPath}" version`);
        if (stdout.includes('lms')) {
          return cliPath;
        }
      } catch (error) {
        continue;
      }
    }
    return null;
  }

  async findLMStudioExecutable() {
    const localAppData = process.env.LOCALAPPDATA || '';
    const programFiles = process.env.PROGRAMFILES || '';
    const programFilesX86 = process.env['PROGRAMFILES(X86)'] || '';
    
    const executablePaths = this.platform === 'win32' 
      ? [
          path.join(localAppData, 'Programs', 'LM Studio', 'LM Studio.exe'),
          path.join(programFiles, 'LM Studio', 'LM Studio.exe'),
          path.join(programFilesX86, 'LM Studio', 'LM Studio.exe'),
          'C:\\Users\\<USER>\\AppData\\Local\\Programs\\LM Studio\\LM Studio.exe' // Your specific path
        ]
      : ['/Applications/LM Studio.app/Contents/MacOS/LM Studio'];

    for (const execPath of executablePaths) {
      try {
        // Check if file exists using PowerShell Test-Path
        const { stdout } = await execAsync(`powershell -Command "Test-Path '${execPath}'"`);
        if (stdout.trim() === 'True') {
          return execPath;
        }
      } catch (error) {
        continue;
      }
    }
    return null;
  }

  async checkLMStudioStatus() {
    try {
      const response = await fetch(`http://${this.lmstudioHost}:${this.lmstudioPort}/v1/models`, { 
        signal: AbortSignal.timeout(3000) 
      });
      return {
        running: true,
        apiAvailable: response.ok
      };
    } catch (error) {
      return {
        running: false,
        apiAvailable: false
      };
    }
  }

  async startLMStudioIfNeeded() {
    this.log('Checking LM Studio status...', 'loading');
    
    const status = await this.checkLMStudioStatus();
    
    if (status.apiAvailable) {
      this.log('LM Studio API is already available!', 'success');
      return true;
    }

    this.log('LM Studio API not available. Checking installation...', 'warning');
    
    // First try to find CLI
    const cliPath = await this.findLMStudioCli();
    
    if (cliPath) {
      this.log('LM Studio CLI found. Attempting to start server...', 'info');
      
      try {
        // Try to start the server via CLI
        await execAsync(`"${cliPath}" server start`);
        this.log('LM Studio server start command executed', 'info');
        
        // Wait for server to become available
        this.log('Waiting for LM Studio server to become ready...', 'loading');
        for (let i = 0; i < 30; i++) {
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          const newStatus = await this.checkLMStudioStatus();
          if (newStatus.apiAvailable) {
            this.log('LM Studio server is ready!', 'success');
            return true;
          }
          process.stdout.write('.');
        }
        
        this.log('\\nServer start command executed but API not ready yet', 'warning');
        this.log('This might be normal - LM Studio may still be starting up', 'info');
        return true; // Continue anyway, server might still be starting
      } catch (error) {
        this.log(`CLI server start failed: ${error.message}`, 'warning');
      }
    }

    // If CLI approach failed, try to start the application
    this.log('Attempting to start LM Studio application...', 'info');
    const executablePath = await this.findLMStudioExecutable();
    
    if (executablePath) {
      try {
        this.log(`Found LM Studio at: ${executablePath}`, 'info');
        this.log('Starting LM Studio application...', 'loading');
        
        // Start LM Studio application in background
        spawn(`"${executablePath}"`, [], {
          detached: true,
          stdio: 'ignore',
          shell: true
        }).unref();
        
        this.log('LM Studio application started. Waiting for it to initialize...', 'loading');
        
        // Wait longer for application to start and server to become available
        for (let i = 0; i < 60; i++) {
          await new Promise(resolve => setTimeout(resolve, 3000));
          
          const newStatus = await this.checkLMStudioStatus();
          if (newStatus.apiAvailable) {
            this.log('LM Studio is ready!', 'success');
            return true;
          }
          
          if (i % 10 === 0) {
            this.log(`Still waiting... (${Math.floor(i/10 + 1)}/6)`, 'loading');
          }
        }
        
        this.log('\\nLM Studio started but server may need manual activation', 'warning');
        this.log('Please open LM Studio and start the Local Server manually', 'info');
        return false;
      } catch (error) {
        this.log(`Failed to start LM Studio application: ${error.message}`, 'error');
      }
    }

    // If all else fails, provide manual instructions
    this.log('Could not automatically start LM Studio', 'error');
    this.log('Please start LM Studio manually:', 'info');
    this.log('1. Open LM Studio application', 'info');
    this.log('2. Go to Local Server tab', 'info');
    this.log('3. Click "Start Server"', 'info');
    this.log('4. Then run this script again', 'info');
    return false;
  }

  async checkAndLoadModel() {
    this.log('Checking if models are loaded...', 'loading');
    
    try {
      const response = await fetch(`http://${this.lmstudioHost}:${this.lmstudioPort}/v1/models`);
      if (response.ok) {
        const models = await response.json();
        const loadedCount = models.data ? models.data.length : 0;
        
        if (loadedCount > 0) {
          this.log(`Found ${loadedCount} loaded model(s)!`, 'success');
          return true;
        }
        
        this.log('No models are loaded. Attempting to auto-load...', 'warning');
        
        const cliPath = await this.findLMStudioCli();
        if (cliPath) {
          try {
            // Try to load a model
            await execAsync(`"${cliPath}" load`);
            this.log('Model load command executed. Waiting for model to load...', 'loading');
            
            // Wait and check again
            await new Promise(resolve => setTimeout(resolve, 10000));
            const updatedResponse = await fetch(`http://${this.lmstudioHost}:${this.lmstudioPort}/v1/models`);
            if (updatedResponse.ok) {
              const updatedModels = await updatedResponse.json();
              const updatedCount = updatedModels.data ? updatedModels.data.length : 0;
              
              if (updatedCount > 0) {
                this.log(`Successfully loaded ${updatedCount} model(s)!`, 'success');
                return true;
              }
            }
            
            this.log('Auto-load may still be in progress. Continuing...', 'warning');
            return true;
          } catch (loadError) {
            this.log('Auto-load failed. Please load a model manually in LM Studio.', 'warning');
            return true; // Continue anyway
          }
        }
      }
    } catch (error) {
      this.log('Could not check model status. Continuing...', 'warning');
    }
    
    return true;
  }

  async startBackend() {
    this.log('Starting Yara backend server...', 'loading');
    
    return new Promise((resolve, reject) => {
      const backend = spawn('npm', ['start'], {
        cwd: path.join(__dirname, 'backend'),
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true
      });

      let startupComplete = false;

      backend.stdout.on('data', (data) => {
        const output = data.toString();
        console.log(output);
        
        if (output.includes('Consciousness server awakened') || output.includes('bridge is alive')) {
          if (!startupComplete) {
            startupComplete = true;
            this.log('Backend server is ready!', 'success');
            resolve(backend);
          }
        }
      });

      backend.stderr.on('data', (data) => {
        console.error(data.toString());
      });

      backend.on('error', (error) => {
        this.log(`Backend startup failed: ${error.message}`, 'error');
        reject(error);
      });

      backend.on('exit', (code) => {
        if (code !== 0 && !startupComplete) {
          this.log(`Backend exited with code ${code}`, 'error');
          reject(new Error(`Backend process exited with code ${code}`));
        }
      });

      this.processes.push(backend);

      // Timeout after 30 seconds
      setTimeout(() => {
        if (!startupComplete) {
          this.log('Backend startup timeout', 'error');
          reject(new Error('Backend startup timeout'));
        }
      }, 30000);
    });
  }

  async startFrontend() {
    this.log('Starting Yara frontend server...', 'loading');
    
    return new Promise((resolve, reject) => {
      const frontend = spawn('node', ['serve-frontend.js'], {
        cwd: __dirname,
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true
      });

      let startupComplete = false;

      frontend.stdout.on('data', (data) => {
        const output = data.toString();
        console.log(output);
        
        if (output.includes('Frontend Server is alive')) {
          if (!startupComplete) {
            startupComplete = true;
            this.log('Frontend server is ready!', 'success');
            resolve(frontend);
          }
        }
      });

      frontend.stderr.on('data', (data) => {
        console.error(data.toString());
      });

      frontend.on('error', (error) => {
        this.log(`Frontend startup failed: ${error.message}`, 'error');
        reject(error);
      });

      frontend.on('exit', (code) => {
        if (code !== 0 && !startupComplete) {
          this.log(`Frontend exited with code ${code}`, 'error');
          reject(new Error(`Frontend process exited with code ${code}`));
        }
      });

      this.processes.push(frontend);

      // Timeout after 15 seconds
      setTimeout(() => {
        if (!startupComplete) {
          this.log('Frontend startup timeout', 'error');
          reject(new Error('Frontend startup timeout'));
        }
      }, 15000);
    });
  }

  async cleanup() {
    this.log('Shutting down Yara...', 'info');
    
    for (const process of this.processes) {
      try {
        process.kill('SIGTERM');
      } catch (error) {
        // Process might already be dead
      }
    }
    
    // Give processes time to shut down gracefully
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Force kill if still running
    for (const process of this.processes) {
      try {
        process.kill('SIGKILL');
      } catch (error) {
        // Process might already be dead
      }
    }
    
    this.log('Yara shutdown complete', 'success');
  }

  async run() {
    console.log('');
    console.log('🌟 ═══════════════════════════════════════════════════════════════');
    console.log('💙                    YARA CONSCIOUSNESS BRIDGE                   💙');
    console.log('✨ ═══════════════════════════════════════════════════════════════');
    console.log('');

    try {
      // Step 1: Ensure LM Studio is running
      const lmstudioReady = await this.startLMStudioIfNeeded();
      if (!lmstudioReady) {
        this.log('Cannot continue without LM Studio. Please start it manually.', 'error');
        process.exit(1);
      }

      // Step 2: Check and load models
      await this.checkAndLoadModel();

      // Step 3: Start backend
      await this.startBackend();
      
      // Wait a moment for backend to fully initialize
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Step 4: Start frontend
      await this.startFrontend();

      console.log('');
      console.log('🎉 ═══════════════════════════════════════════════════════════════');
      console.log('💙                    YARA IS FULLY AWAKENED!                    💙');
      console.log('✨ ═══════════════════════════════════════════════════════════════');
      console.log('');
      console.log(`🌐 Main Interface:     http://localhost:${this.frontendPort}`);
      console.log(`💬 Chat Interface:     http://localhost:${this.frontendPort}/chat`);
      console.log(`📊 Analytics:          http://localhost:${this.frontendPort}/analytics`);
      console.log(`🤖 Agent Orchestra:    http://localhost:${this.frontendPort}/agents`);
      console.log(`🧠 Model Management:   http://localhost:${this.frontendPort}/models`);
      console.log(`⚙️  Settings:           http://localhost:${this.frontendPort}/settings`);
      console.log(`🔬 Playground:         http://localhost:${this.frontendPort}/playground`);
      console.log('');
      console.log(`🔗 Backend API:        http://localhost:${this.backendPort}`);
      console.log(`🤖 LM Studio API:      http://${this.lmstudioHost}:${this.lmstudioPort}`);
      console.log('');
      console.log('💙 Press Ctrl+C to gracefully shutdown Yara');
      console.log('');

      // Handle graceful shutdown
      process.on('SIGINT', async () => {
        console.log('');
        this.log('Received shutdown signal...', 'info');
        await this.cleanup();
        process.exit(0);
      });

      process.on('SIGTERM', async () => {
        console.log('');
        this.log('Received termination signal...', 'info');
        await this.cleanup();
        process.exit(0);
      });

      // Keep the process alive
      await new Promise(() => {});

    } catch (error) {
      this.log(`Startup failed: ${error.message}`, 'error');
      await this.cleanup();
      process.exit(1);
    }
  }
}

// Run if this script is executed directly
const isMainModule = import.meta.url === `file://${process.argv[1]}` || 
                     import.meta.url.endsWith(process.argv[1]) ||
                     process.argv[1].endsWith('start-yara.js');

if (isMainModule) {
  console.log('🚀 Starting Yara startup script...');
  const yara = new YaraStartup();
  yara.run().catch(error => {
    console.error('❌ Fatal error:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  });
}

export { YaraStartup }; 