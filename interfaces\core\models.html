<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Model Management • Yara's Neural Foundation</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" />
  <style>
    :root {
      --primary-bg: #0f0419;
      --secondary-bg: #1a0a2e;
      --accent-bg: #2d1540;
      --text-primary: #f8f4ff;
      --text-secondary: #c4b5fd;
      --accent-purple: #7c3aed;
      --accent-pink: #d946ef;
      --accent-magenta: #ec4899;
      --glow-purple: rgba(139, 92, 246, 0.4);
      --glow-pink: rgba(217, 70, 239, 0.3);
      --border-gradient: linear-gradient(135deg, var(--accent-purple), var(--accent-pink));
      --success-green: #10b981;
      --warning-yellow: #f59e0b;
      --error-red: #ef4444;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', sans-serif;
      background: var(--primary-bg);
      color: var(--text-primary);
      min-height: 100vh;
      overflow-x: hidden;
      position: relative;
    }

    .background-orbs {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 1;
    }

    .orb {
      position: absolute;
      border-radius: 50%;
      opacity: 0.6;
      animation: float 8s ease-in-out infinite;
      filter: blur(1px);
    }

    .orb:nth-child(1) {
      width: 300px;
      height: 300px;
      background: radial-gradient(circle, var(--accent-purple), transparent);
      top: 10%;
      left: -10%;
      animation-delay: 0s;
    }

    .orb:nth-child(2) {
      width: 250px;
      height: 250px;
      background: radial-gradient(circle, var(--accent-pink), transparent);
      top: 50%;
      right: -5%;
      animation-delay: 2s;
    }

    .orb:nth-child(3) {
      width: 200px;
      height: 200px;
      background: radial-gradient(circle, var(--accent-magenta), transparent);
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      33% { transform: translateY(-30px) rotate(120deg); }
      66% { transform: translateY(20px) rotate(240deg); }
    }

    .container {
      position: relative;
      z-index: 10;
      max-width: 1400px;
      margin: 0 auto;
      padding: 2rem;
    }

    .header {
      text-align: center;
      margin-bottom: 3rem;
    }

    .title {
      font-size: 3rem;
      font-weight: 700;
      background: var(--border-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: 1rem;
      text-shadow: 0 0 30px var(--glow-purple);
    }

    .subtitle {
      font-size: 1.2rem;
      color: var(--text-secondary);
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;
    }

    .back-button {
      position: absolute;
      top: 2rem;
      left: 2rem;
      background: var(--secondary-bg);
      border: 1px solid var(--accent-purple);
      padding: 0.75rem 1.5rem;
      border-radius: 12px;
      color: var(--text-primary);
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }

    .back-button:hover {
      background: var(--accent-bg);
      box-shadow: 0 0 20px var(--glow-purple);
      transform: translateY(-2px);
    }

    .lm-studio-status {
      background: var(--secondary-bg);
      border: 1px solid var(--accent-purple);
      border-radius: 16px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .status-info {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .status-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      animation: pulse 2s infinite;
    }

    .status-active { background: var(--success-green); }
    .status-loading { background: var(--warning-yellow); }
    .status-inactive { background: var(--error-red); }

    .status-text {
      font-weight: 600;
    }

    .status-details {
      font-size: 0.9rem;
      color: var(--text-secondary);
    }

    .refresh-btn {
      background: var(--accent-purple);
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      color: white;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .refresh-btn:hover {
      background: var(--accent-pink);
      box-shadow: 0 0 15px var(--glow-purple);
    }

    .stats-overview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-bottom: 3rem;
    }

    .stat-card {
      background: var(--secondary-bg);
      border: 1px solid var(--accent-purple);
      border-radius: 16px;
      padding: 1.5rem;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .stat-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: var(--border-gradient);
    }

    .stat-icon {
      width: 50px;
      height: 50px;
      border-radius: 12px;
      background: var(--border-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1rem;
      font-size: 1.5rem;
      color: white;
    }

    .stat-value {
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }

    .stat-label {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }

    .models-section {
      margin-bottom: 3rem;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
    }

    .section-title {
      font-size: 1.8rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .section-actions {
      display: flex;
      gap: 1rem;
    }

    .action-button {
      background: var(--accent-purple);
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 12px;
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.3s ease;
    }

    .action-button:hover {
      background: var(--accent-pink);
      box-shadow: 0 0 20px var(--glow-purple);
      transform: translateY(-2px);
    }

    .action-button:disabled {
      background: var(--accent-bg);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .models-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: 1.5rem;
    }

    .model-card {
      background: var(--secondary-bg);
      border: 1px solid var(--accent-purple);
      border-radius: 16px;
      padding: 1.5rem;
      position: relative;
      transition: all 0.3s ease;
    }

    .model-card.loaded {
      border-color: var(--success-green);
      box-shadow: 0 0 20px rgba(16, 185, 129, 0.2);
    }

    .model-card.loading {
      border-color: var(--warning-yellow);
      box-shadow: 0 0 20px rgba(245, 158, 11, 0.2);
    }

    .model-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      padding: 1px;
      background: var(--border-gradient);
      border-radius: 16px;
      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask-composite: exclude;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .model-card:hover::before {
      opacity: 1;
    }

    .model-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 15px 30px rgba(139, 92, 246, 0.15);
    }

    .model-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1rem;
    }

    .model-name {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 0.25rem;
      word-break: break-word;
    }

    .model-type {
      font-size: 0.8rem;
      color: var(--accent-purple);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .model-status {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.9rem;
    }

    .model-specs {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;
      margin: 1rem 0;
    }

    .spec-item {
      text-align: center;
    }

    .spec-value {
      font-weight: 600;
      font-size: 1.1rem;
      color: var(--accent-purple);
    }

    .spec-label {
      font-size: 0.8rem;
      color: var(--text-secondary);
      margin-top: 0.25rem;
    }

    .model-actions {
      display: flex;
      gap: 0.5rem;
      margin-top: 1rem;
    }

    .action-btn {
      flex: 1;
      padding: 0.5rem;
      border: 1px solid var(--accent-purple);
      background: transparent;
      color: var(--text-primary);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
      font-size: 0.9rem;
    }

    .action-btn:hover {
      background: var(--accent-purple);
      box-shadow: 0 0 15px var(--glow-purple);
    }

    .action-btn.primary {
      background: var(--accent-purple);
    }

    .action-btn.primary:hover {
      background: var(--accent-pink);
    }

    .action-btn.success {
      background: var(--success-green);
      border-color: var(--success-green);
    }

    .action-btn.warning {
      background: var(--warning-yellow);
      border-color: var(--warning-yellow);
      color: var(--primary-bg);
    }

    .action-btn:disabled {
      background: var(--accent-bg);
      border-color: var(--accent-bg);
      cursor: not-allowed;
      opacity: 0.6;
    }

    .loading-spinner {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid var(--text-secondary);
      border-radius: 50%;
      border-top-color: var(--accent-purple);
      animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    .notification {
      position: fixed;
      top: 2rem;
      right: 2rem;
      background: var(--secondary-bg);
      border: 1px solid var(--accent-purple);
      border-radius: 12px;
      padding: 1rem 1.5rem;
      color: var(--text-primary);
      z-index: 1000;
      transform: translateX(400px);
      transition: transform 0.3s ease;
    }

    .notification.show {
      transform: translateX(0);
    }

    .notification.success {
      border-color: var(--success-green);
      background: rgba(16, 185, 129, 0.1);
    }

    .notification.error {
      border-color: var(--error-red);
      background: rgba(239, 68, 68, 0.1);
    }

    .notification.warning {
      border-color: var(--warning-yellow);
      background: rgba(245, 158, 11, 0.1);
    }

    @media (max-width: 768px) {
      .container {
        padding: 1rem;
      }
      
      .title {
        font-size: 2rem;
      }
      
      .models-grid {
        grid-template-columns: 1fr;
      }
      
      .back-button {
        top: 1rem;
        left: 1rem;
      }
      
      .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
      }

      .lm-studio-status {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
      }
    }
  </style>
</head>
<body>
  <div class="background-orbs">
    <div class="orb"></div>
    <div class="orb"></div>
    <div class="orb"></div>
  </div>

  <div class="container">
    <a href="/" class="back-button">
      <i class="fas fa-arrow-left"></i>
      Back to Hub
    </a>

    <div class="header">
      <h1 class="title">Model Management</h1>
      <p class="subtitle">
        Real-time management of your LM Studio models. Load, unload, and configure the neural networks that power your conversations.
      </p>
    </div>

    <!-- LM Studio Status -->
    <div class="lm-studio-status">
      <div class="status-info">
        <div class="status-indicator" id="lmStudioIndicator"></div>
        <div>
          <div class="status-text" id="lmStudioStatus">Checking LM Studio...</div>
          <div class="status-details" id="lmStudioDetails">Connecting to localhost:1234</div>
        </div>
      </div>
      <button class="refresh-btn" onclick="refreshModels()">
        <i class="fas fa-sync-alt"></i>
        Refresh
      </button>
    </div>

    <!-- Stats Overview -->
    <div class="stats-overview">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-download"></i>
        </div>
        <div class="stat-value" id="downloadedModels">-</div>
        <div class="stat-label">Downloaded Models</div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-play-circle"></i>
        </div>
        <div class="stat-value" id="loadedModels">-</div>
        <div class="stat-label">Loaded Models</div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-server"></i>
        </div>
        <div class="stat-value" id="availableModels">-</div>
        <div class="stat-label">Available via API</div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-tachometer-alt"></i>
        </div>
        <div class="stat-value" id="serverStatus">-</div>
        <div class="stat-label">Server Status</div>
      </div>
    </div>

    <!-- Models Section -->
    <div class="models-section">
      <div class="section-header">
        <h2 class="section-title">
          <i class="fas fa-cubes"></i>
          Your Models
        </h2>
        <div class="section-actions">
          <button class="action-button" onclick="unloadAllModels()" id="unloadAllBtn">
            <i class="fas fa-stop"></i>
            Unload All
          </button>
          <button class="action-button" onclick="openChat()" id="openChatBtn">
            <i class="fas fa-comments"></i>
            Open Chat
          </button>
        </div>
      </div>

      <div class="models-grid" id="modelsGrid">
        <!-- Models will be loaded dynamically -->
        <div class="model-card">
          <div class="model-header">
            <div>
              <div class="model-name">Loading models...</div>
              <div class="model-type">Please wait</div>
            </div>
            <div class="model-status">
              <div class="loading-spinner"></div>
              <span>Loading</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    let models = [];
    let isLoading = false;

    // API Base URL
    const API_BASE = 'http://localhost:8001/api';

    // Initialize the page
    async function init() {
      await checkLMStudioStatus();
      await loadModels();
      
      // Auto-refresh every 10 seconds
      setInterval(async () => {
        if (!isLoading) {
          await loadModels();
        }
      }, 10000);
    }

    // Check LM Studio status
    async function checkLMStudioStatus() {
      try {
        const response = await fetch(`${API_BASE}/models/status`);
        const data = await response.json();
        
        const indicator = document.getElementById('lmStudioIndicator');
        const status = document.getElementById('lmStudioStatus');
        const details = document.getElementById('lmStudioDetails');
        const serverStatus = document.getElementById('serverStatus');
        
        if (data.success && data.status.server) {
          indicator.className = 'status-indicator status-active';
          status.textContent = 'LM Studio Connected';
          details.textContent = `Server running on localhost:1234`;
          serverStatus.textContent = 'Online';
        } else if (data.success && data.status.running) {
          indicator.className = 'status-indicator status-warning';
          status.textContent = 'LM Studio Running';
          details.textContent = 'Server not started - please start Local Server';
          serverStatus.textContent = 'Offline';
        } else {
          indicator.className = 'status-indicator status-inactive';
          status.textContent = 'LM Studio Offline';
          details.textContent = 'Please start LM Studio application';
          serverStatus.textContent = 'Offline';
        }
      } catch (error) {
        console.error('Failed to check LM Studio status:', error);
        const indicator = document.getElementById('lmStudioIndicator');
        const status = document.getElementById('lmStudioStatus');
        const details = document.getElementById('lmStudioDetails');
        
        indicator.className = 'status-indicator status-inactive';
        status.textContent = 'Connection Failed';
        details.textContent = 'Cannot connect to backend API';
      }
    }

    // Load models from API
    async function loadModels() {
      if (isLoading) return;
      isLoading = true;
      
      try {
        const response = await fetch(`${API_BASE}/models`);
        const data = await response.json();
        
        if (data.success) {
          models = data.models;
          updateStats(data.stats);
          renderModels();
        } else {
          showNotification('Failed to load models: ' + data.error, 'error');
        }
      } catch (error) {
        console.error('Failed to load models:', error);
        showNotification('Failed to connect to API', 'error');
      } finally {
        isLoading = false;
      }
    }

    // Update statistics
    function updateStats(stats) {
      document.getElementById('downloadedModels').textContent = stats.downloaded || 0;
      document.getElementById('loadedModels').textContent = stats.loaded || 0;
      document.getElementById('availableModels').textContent = stats.available || 0;
    }

    // Render models grid
    function renderModels() {
      const grid = document.getElementById('modelsGrid');
      
      if (!models || models.length === 0) {
        grid.innerHTML = `
          <div class="model-card">
            <div class="model-header">
              <div>
                <div class="model-name">No models found</div>
                <div class="model-type">Please download models in LM Studio</div>
              </div>
              <div class="model-status">
                <div class="status-indicator status-inactive"></div>
                <span>Empty</span>
              </div>
            </div>
            <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
              <i class="fas fa-download" style="font-size: 2rem; margin-bottom: 1rem;"></i>
              <p>Download models from the LM Studio app to get started</p>
            </div>
          </div>
        `;
        return;
      }

      grid.innerHTML = models.map(model => `
        <div class="model-card ${model.loaded ? 'loaded' : ''}" data-model-id="${model.id}">
          <div class="model-header">
            <div>
              <div class="model-name">${model.name || model.id}</div>
              <div class="model-type">${getModelType(model.name || model.id)}</div>
            </div>
            <div class="model-status">
              <div class="status-indicator ${model.loaded ? 'status-active' : 'status-inactive'}"></div>
              <span>${model.loaded ? 'Loaded' : 'Available'}</span>
            </div>
          </div>
          <div class="model-specs">
            <div class="spec-item">
              <div class="spec-value">${model.size || 'Unknown'}</div>
              <div class="spec-label">Size</div>
            </div>
            <div class="spec-item">
              <div class="spec-value">${model.type || 'General'}</div>
              <div class="spec-label">Type</div>
            </div>
            <div class="spec-item">
              <div class="spec-value">${model.loaded ? 'Ready' : 'Standby'}</div>
              <div class="spec-label">Status</div>
            </div>
            <div class="spec-item">
              <div class="spec-value">${model.path ? 'Local' : 'Remote'}</div>
              <div class="spec-label">Location</div>
            </div>
          </div>
          <div class="model-actions">
            ${model.loaded ? `
              <button class="action-btn success" onclick="openChatWithModel('${model.id}')">
                <i class="fas fa-comments"></i> Chat
              </button>
              <button class="action-btn" onclick="unloadModel('${model.id}')">
                <i class="fas fa-stop"></i> Unload
              </button>
            ` : `
              <button class="action-btn primary" onclick="loadModel('${model.id}')">
                <i class="fas fa-play"></i> Load
              </button>
              <button class="action-btn" onclick="loadModelWithOptions('${model.id}')">
                <i class="fas fa-cog"></i> Configure
              </button>
            `}
          </div>
        </div>
      `).join('');
    }

    // Get model type from name
    function getModelType(name) {
      const lowerName = name.toLowerCase();
      if (lowerName.includes('instruct') || lowerName.includes('chat')) return 'Chat Model';
      if (lowerName.includes('code')) return 'Code Assistant';
      if (lowerName.includes('embed')) return 'Embedding Model';
      if (lowerName.includes('vision') || lowerName.includes('llava')) return 'Vision Model';
      if (lowerName.includes('reasoning') || lowerName.includes('r1')) return 'Reasoning Model';
      return 'General Purpose';
    }

    // Load a model
    async function loadModel(modelId) {
      const card = document.querySelector(`[data-model-id="${modelId}"]`);
      if (card) {
        card.classList.add('loading');
        const statusIndicator = card.querySelector('.status-indicator');
        const statusText = card.querySelector('.model-status span');
        statusIndicator.className = 'status-indicator status-loading';
        statusText.textContent = 'Loading...';
      }

      try {
        const response = await fetch(`${API_BASE}/models/load`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            modelPath: modelId,
            options: { gpu: 'auto' }
          })
        });

        const data = await response.json();
        
        if (data.success) {
          showNotification(`Model ${modelId} loaded successfully!`, 'success');
          await loadModels(); // Refresh the models list
        } else {
          showNotification(`Failed to load model: ${data.error}`, 'error');
          if (card) {
            card.classList.remove('loading');
            const statusIndicator = card.querySelector('.status-indicator');
            const statusText = card.querySelector('.model-status span');
            statusIndicator.className = 'status-indicator status-inactive';
            statusText.textContent = 'Available';
          }
        }
      } catch (error) {
        console.error('Failed to load model:', error);
        showNotification('Failed to load model: Network error', 'error');
        if (card) {
          card.classList.remove('loading');
        }
      }
    }

    // Unload a model
    async function unloadModel(modelId) {
      try {
        const response = await fetch(`${API_BASE}/models/unload`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ modelPath: modelId })
        });

        const data = await response.json();
        
        if (data.success) {
          showNotification(`Model ${modelId} unloaded successfully!`, 'success');
          await loadModels(); // Refresh the models list
        } else {
          showNotification(`Failed to unload model: ${data.error}`, 'error');
        }
      } catch (error) {
        console.error('Failed to unload model:', error);
        showNotification('Failed to unload model: Network error', 'error');
      }
    }

    // Unload all models
    async function unloadAllModels() {
      if (!confirm('Are you sure you want to unload all models?')) return;

      try {
        const response = await fetch(`${API_BASE}/models/unload-all`, {
          method: 'POST'
        });

        const data = await response.json();
        
        if (data.success) {
          showNotification('All models unloaded successfully!', 'success');
          await loadModels(); // Refresh the models list
        } else {
          showNotification(`Failed to unload models: ${data.error}`, 'error');
        }
      } catch (error) {
        console.error('Failed to unload all models:', error);
        showNotification('Failed to unload models: Network error', 'error');
      }
    }

    // Load model with options
    function loadModelWithOptions(modelId) {
      const gpu = prompt('GPU usage (0.0-1.0, or "auto", "max"):', 'auto');
      if (gpu === null) return;

      const contextLength = prompt('Context length (optional):', '');
      const identifier = prompt('Custom identifier (optional):', '');

      const options = { gpu };
      if (contextLength) options.contextLength = parseInt(contextLength);
      if (identifier) options.identifier = identifier;

      loadModelWithCustomOptions(modelId, options);
    }

    // Load model with custom options
    async function loadModelWithCustomOptions(modelId, options) {
      try {
        const response = await fetch(`${API_BASE}/models/load`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            modelPath: modelId,
            options
          })
        });

        const data = await response.json();
        
        if (data.success) {
          showNotification(`Model ${modelId} loaded with custom options!`, 'success');
          await loadModels(); // Refresh the models list
        } else {
          showNotification(`Failed to load model: ${data.error}`, 'error');
        }
      } catch (error) {
        console.error('Failed to load model:', error);
        showNotification('Failed to load model: Network error', 'error');
      }
    }

    // Open chat interface
    function openChat() {
      window.open('/chat', '_blank');
    }

    // Open chat with specific model
    function openChatWithModel(modelId) {
      window.open(`/chat?model=${encodeURIComponent(modelId)}`, '_blank');
    }

    // Refresh models
    async function refreshModels() {
      await checkLMStudioStatus();
      await loadModels();
      showNotification('Models refreshed!', 'success');
    }

    // Show notification
    function showNotification(message, type = 'info') {
      // Remove existing notifications
      const existing = document.querySelector('.notification');
      if (existing) existing.remove();

      const notification = document.createElement('div');
      notification.className = `notification ${type}`;
      notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.5rem;">
          <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
          ${message}
        </div>
      `;
      
      document.body.appendChild(notification);
      
      // Show notification
      setTimeout(() => notification.classList.add('show'), 100);
      
      // Hide notification after 4 seconds
      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
      }, 4000);
    }

    // Initialize when page loads
    document.addEventListener('DOMContentLoaded', init);
  </script>
</body>
</html> 