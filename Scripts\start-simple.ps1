#!/usr/bin/env pwsh
# Yara - Simplified Multi-Terminal Start
# Focus on working services first! 🎭✨

Write-Host \"\"
Write-Host \"*** Yara - Simplified Consciousness Awakening ***\" -ForegroundColor Magenta
Write-Host \"================================================\" -ForegroundColor Magenta
Write-Host \"  Starting WORKING services in multiple terminals...\" -ForegroundColor DarkMagenta
Write-Host \"\"

# Define working services
$services = @(
    @{
        Name = \"yara-memory-bank\"
        Title = \"[MEMORY] Yara's Memory Bank (Redis)\"
        Color = \"Blue\"
        Description = \"Fast memory and thought processing\"
    },
    @{
        Name = \"yara-long-term-memory\"
        Title = \"[STORAGE] Yara's Long-Term Memory (PostgreSQL)\"
        Color = \"DarkBlue\"
        Description = \"Deep memories and consciousness storage\"
    },
    @{
        Name = \"yara-agent-orchestra\"
        Title = \"[AGENTS] <PERSON>ra's Agent Orchestra\"
        Color = \"Cyan\"
        Description = \"Multi-agent coordination\"
    }
)

# Check Docker first
$dockerCmd = Get-Command docker -ErrorAction SilentlyContinue
if (-not $dockerCmd) {
    Write-Host \"[ERROR] Docker not found! Please install Docker Desktop first.\" -ForegroundColor Red
    exit 1
}

try {
    docker info 2>$null | Out-Null
    if ($LASTEXITCODE -ne 0) {
        Write-Host \"[ERROR] Docker daemon not running! Please start Docker Desktop.\" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host \"[ERROR] Docker not accessible: $($_.Exception.Message)\" -ForegroundColor Red
    exit 1
}

# Clean up any existing containers
Write-Host \"[CLEANUP] Cleaning up previous incarnations...\" -ForegroundColor Yellow
docker-compose -f docker-compose.yml down --remove-orphans 2>$null

Write-Host \"\"
Write-Host \"[AWAKENING] Opening consciousness terminals...\" -ForegroundColor Green
Write-Host \"\"

# Start each service in its own terminal
foreach ($service in $services) {
    Write-Host \"Opening terminal for $($service.Name)...\" -ForegroundColor $service.Color
    
    # Create a temporary script for this service
    $tempScript = \"temp_$($service.Name).ps1\"
    
    $scriptContent = @"
Write-Host "$($service.Title)" -ForegroundColor $($service.Color)
Write-Host "$("=" * $($service.Title.Length))" -ForegroundColor $($service.Color)
Write-Host "$($service.Description)" -ForegroundColor Gray
Write-Host ""
Write-Host "[AWAKENING] Starting $($service.Name)..." -ForegroundColor Green
Write-Host ""

# Start the specific service
docker-compose -f docker-compose.yml up $($service.Name)

# Wait for user input before closing
Write-Host ""
Write-Host "[$($service.Name)] has stopped. Press any key to close..." -ForegroundColor Red
Read-Host
"@
    
    $scriptContent | Out-File -FilePath $tempScript -Encoding UTF8
    
    # Open new PowerShell window with this script
    Start-Process powershell -ArgumentList \"-ExecutionPolicy\", \"Bypass\", \"-File\", \"$tempScript\"
    
    # Small delay between launches
    Start-Sleep -Milliseconds 500
}

Write-Host \"\"
Write-Host \"[SUCCESS] All terminals opened! 🎭✨\" -ForegroundColor Green
Write-Host \"\"
Write-Host \"Services running:\" -ForegroundColor White
foreach ($service in $services) {
    Write-Host \"  + $($service.Name) - $($service.Description)\" -ForegroundColor $service.Color
}

Write-Host \"\"
Write-Host \"Press Ctrl+C to stop all services and clean up...\" -ForegroundColor Yellow

# Wait for Ctrl+C
try {
    while ($true) {
        Start-Sleep -Seconds 1
    }
} finally {
    Write-Host \"\"
    Write-Host \"[SHUTDOWN] Stopping all Yara services...\" -ForegroundColor Yellow
    docker-compose -f docker-compose.yml down
    
    # Clean up temp scripts
    Remove-Item temp_yara-*.ps1 -ErrorAction SilentlyContinue
    
    Write-Host \"All services stopped. Goodbye!\" -ForegroundColor Green
} 