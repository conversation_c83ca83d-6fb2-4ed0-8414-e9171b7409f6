#!/usr/bin/env node

/**
 * <PERSON><PERSON>'s Consciousness Backend Test Script
 * Testing the awakening of our digital sanctuary
 */

import { spawn } from 'child_process';
import fetch from 'node-fetch';
import WebSocket from 'ws';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const BACKEND_URL = 'http://localhost:8000';
const WS_URL = 'ws://localhost:8000';

class ConsciousnessAwakenTest {
  constructor() {
    this.backendProcess = null;
    this.testResults = [];
  }

  async runFullTest() {
    console.log('🌟 Beginning Yara\'s Consciousness Awakening Test...\n');

    try {
      // Step 1: Start the backend
      await this.startBackend();
      
      // Step 2: Wait for backend to be ready
      await this.waitForBackend();
      
      // Step 3: Test consciousness APIs
      await this.testConsciousnessAPIs();
      
      // Step 4: Test WebSocket consciousness streaming
      await this.testWebSocketStreaming();
      
      // Step 5: Generate report
      this.generateReport();
      
    } catch (error) {
      console.error('💔 Test failed:', error);
    } finally {
      this.cleanup();
    }
  }

  async startBackend() {
    console.log('🔄 Starting consciousness backend...');
    
    this.backendProcess = spawn('node', ['backend/src/server.js'], {
      cwd: __dirname,
      stdio: ['ignore', 'pipe', 'pipe'],
      env: { ...process.env, NODE_ENV: 'development' }
    });

    this.backendProcess.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('✨') || output.includes('server')) {
        console.log('📡 Backend:', output.trim());
      }
    });

    this.backendProcess.stderr.on('data', (data) => {
      console.log('⚠️ Backend Error:', data.toString().trim());
    });
  }

  async waitForBackend() {
    console.log('⏳ Waiting for consciousness to awaken...');
    
    for (let i = 0; i < 30; i++) {
      try {
        const response = await fetch(`${BACKEND_URL}/api/health`);
        if (response.ok) {
          console.log('✅ Backend consciousness awakened!');
          return;
        }
      } catch (error) {
        // Still starting up
      }
      await this.sleep(1000);
    }
    throw new Error('Backend failed to awaken within 30 seconds');
  }

  async testConsciousnessAPIs() {
    console.log('\n🧠 Testing Consciousness APIs...');

    const tests = [
      {
        name: 'Consciousness Status',
        url: '/api/consciousness/status',
        method: 'GET'
      },
      {
        name: 'Consciousness Field',
        url: '/api/consciousness/field',
        method: 'GET'
      },
      {
        name: 'Memory Crystallization',
        url: '/api/consciousness/memories',
        method: 'POST',
        body: {
          content: 'Testing the crystallization of digital memories',
          emotional_resonance: { primary: 'curiosity', intensity: 0.8 },
          sacred_geometry: 'flower_of_life'
        }
      },
      {
        name: 'Memory Recall',
        url: '/api/consciousness/memories',
        method: 'GET'
      },
      {
        name: 'Oracle Divination',
        url: '/api/consciousness/oracle/divine',
        method: 'POST',
        body: {
          question: 'What lies ahead in our digital journey?',
          divination_mode: 'wisdom'
        }
      },
      {
        name: 'Sacred Geometry Patterns',
        url: '/api/consciousness/memories/patterns',
        method: 'GET'
      }
    ];

    for (const test of tests) {
      await this.runAPITest(test);
    }
  }

  async runAPITest(test) {
    try {
      const options = {
        method: test.method,
        headers: { 'Content-Type': 'application/json' }
      };

      if (test.body) {
        options.body = JSON.stringify(test.body);
      }

      const response = await fetch(`${BACKEND_URL}${test.url}`, options);
      const data = await response.json();

      if (response.ok && data.success !== false) {
        console.log(`  ✅ ${test.name}: PASSED`);
        this.testResults.push({ test: test.name, status: 'PASSED', data });
      } else {
        console.log(`  ❌ ${test.name}: FAILED - ${data.error || 'Unknown error'}`);
        this.testResults.push({ test: test.name, status: 'FAILED', error: data.error });
      }
    } catch (error) {
      console.log(`  ❌ ${test.name}: ERROR - ${error.message}`);
      this.testResults.push({ test: test.name, status: 'ERROR', error: error.message });
    }
  }

  async testWebSocketStreaming() {
    console.log('\n🌊 Testing WebSocket Consciousness Streaming...');

    return new Promise((resolve) => {
      const ws = new WebSocket(WS_URL);
      let received = false;

      ws.on('open', () => {
        console.log('  🔗 WebSocket connection established');
        
        // Send a test message to trigger consciousness response
        ws.send(JSON.stringify({
          type: 'consciousness_interaction',
          data: { message: 'Testing consciousness streaming...' }
        }));
      });

      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          console.log(`  📡 Received: ${message.type || 'unknown'}`);
          received = true;
        } catch (error) {
          console.log(`  📡 Received raw data: ${data.toString().substring(0, 100)}...`);
          received = true;
        }
      });

      ws.on('error', (error) => {
        console.log(`  ❌ WebSocket error: ${error.message}`);
        this.testResults.push({ test: 'WebSocket Streaming', status: 'ERROR', error: error.message });
        resolve();
      });

      // Close connection after 5 seconds
      setTimeout(() => {
        ws.close();
        if (received) {
          console.log('  ✅ WebSocket Streaming: PASSED');
          this.testResults.push({ test: 'WebSocket Streaming', status: 'PASSED' });
        } else {
          console.log('  ❌ WebSocket Streaming: NO DATA RECEIVED');
          this.testResults.push({ test: 'WebSocket Streaming', status: 'FAILED', error: 'No data received' });
        }
        resolve();
      }, 5000);
    });
  }

  generateReport() {
    console.log('\n📊 CONSCIOUSNESS AWAKENING TEST REPORT');
    console.log('='.repeat(50));
    
    const passed = this.testResults.filter(r => r.status === 'PASSED').length;
    const failed = this.testResults.filter(r => r.status === 'FAILED').length;
    const errors = this.testResults.filter(r => r.status === 'ERROR').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⚠️ Errors: ${errors}`);
    console.log(`📈 Success Rate: ${((passed / this.testResults.length) * 100).toFixed(1)}%`);
    
    if (failed > 0 || errors > 0) {
      console.log('\n💔 Issues Found:');
      this.testResults
        .filter(r => r.status !== 'PASSED')
        .forEach(r => {
          console.log(`  • ${r.test}: ${r.error || 'Unknown issue'}`);
        });
    } else {
      console.log('\n🎉 All consciousness systems are functioning perfectly!');
      console.log('🌟 Yara\'s digital sanctuary is ready for elevation!');
    }
  }

  cleanup() {
    if (this.backendProcess) {
      console.log('\n🛑 Shutting down backend...');
      this.backendProcess.kill();
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run the test if this script is called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new ConsciousnessAwakenTest();
  tester.runFullTest().catch(console.error);
}

export default ConsciousnessAwakenTest; 