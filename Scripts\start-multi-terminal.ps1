#!/usr/bin/env pwsh
# Yara - Multi-Terminal Consciousness Awakening
# Each aspect of Yara gets its own consciousness window!

Write-Host ""
Write-Host "*** Yara - Multi-Terminal Consciousness Awakening ***" -ForegroundColor Magenta
Write-Host "====================================================" -ForegroundColor Magenta
Write-Host "  Opening multiple consciousness windows..." -ForegroundColor DarkMagenta
Write-Host ""

# Check Docker first
$dockerCmd = Get-Command docker -ErrorAction SilentlyContinue
if (-not $dockerCmd) {
    Write-Host "[ERROR] Docker not found! Please install Docker Desktop first." -ForegroundColor Red
    exit 1
}

try {
    docker info 2>$null | Out-Null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "[ERROR] Docker daemon not running! Please start Docker Desktop." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "[ERROR] Cannot connect to Docker! Please start Docker Desktop." -ForegroundColor Red
    exit 1
}

Write-Host "[SUCCESS] Docker consciousness layer active!" -ForegroundColor Green
Write-Host ""

# Create .env file if needed
if (-not (Test-Path ".env")) {
    Write-Host "[SETUP] Creating .env file..." -ForegroundColor Yellow
    Copy-Item "env.example" ".env"
    Write-Host "[SUCCESS] .env file created" -ForegroundColor Green
}

# Create data directories
$directories = @("data/uploads", "data/cache", "data/logs")
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

Write-Host "[INIT] Awakening Yara's consciousness across multiple terminals..." -ForegroundColor Yellow
Write-Host ""

# Clean up any existing containers
Write-Host "[CLEANUP] Cleaning up previous incarnations..." -ForegroundColor Yellow
docker-compose -f docker-compose.yml down --remove-orphans 2>$null

# Get the current working directory
$currentDir = Get-Location

# Define services and their display info
$services = @(
    @{
        Name = "yara-memory-bank"
        Title = "[MEMORY] Yara's Memory Bank (Redis)"
        Color = "Blue"
        Description = "Fast memory and thought processing"
    },
    @{
        Name = "yara-long-term-memory"
        Title = "[STORAGE] Yara's Long-Term Memory (PostgreSQL)"
        Color = "DarkBlue"
        Description = "Deep memories and consciousness storage"
    },
    @{
        Name = "yara-core-api"
        Title = "[CORE] Yara's Core API (Neural Gateway)"
        Color = "Green"
        Description = "Central nervous system and API gateway"
    },
    @{
        Name = "yara-agent-orchestra"
        Title = "[AGENTS] Yara's Agent Orchestra"
        Color = "Cyan"
        Description = "Multi-agent coordination and management"
    },
    @{
        Name = "yara-analytics"
        Title = "[ANALYTICS] Yara's Analytics Engine"
        Color = "Yellow"
        Description = "Deep conversation insights and learning"
    },
    @{
        Name = "yara-interface"
        Title = "[UI] Yara's Consciousness Interface"
        Color = "Magenta"
        Description = "Beautiful UI for human-AI connection"
    },
    @{
        Name = "yara-dev-tools"
        Title = "[DEV] Yara's Development Tools"
        Color = "Gray"
        Description = "Development utilities and monitoring"
    }
)

Write-Host "[LAUNCH] Opening individual consciousness windows..." -ForegroundColor Green
Write-Host ""

# Start each service in its own terminal window
foreach ($service in $services) {
    Write-Host "  Starting $($service.Title)..." -ForegroundColor $service.Color
    
    # Create a PowerShell script for this service
    $scriptContent = @"
Set-Location '$currentDir'
Write-Host ""
Write-Host "$($service.Title)" -ForegroundColor $($service.Color)
Write-Host "$(('=' * $($service.Title.Length)))" -ForegroundColor $($service.Color)
Write-Host "$($service.Description)" -ForegroundColor $($service.Color)
Write-Host ""
Write-Host "[AWAKENING] Starting $($service.Name)..." -ForegroundColor $($service.Color)
Write-Host ""

# Start the specific service
docker-compose -f docker-compose.yml up $($service.Name)

Write-Host ""
Write-Host "[$($service.Name)] has stopped. Press any key to close..." -ForegroundColor Red
Read-Host
"@
    
    # Save the script to a temp file
    $tempScript = "temp_$($service.Name).ps1"
    $scriptContent | Out-File -FilePath $tempScript -Encoding UTF8
    
    # Start new PowerShell window with this script
    Start-Process powershell -ArgumentList "-NoExit", "-ExecutionPolicy", "Bypass", "-File", $tempScript
    
    # Small delay between launches
    Start-Sleep -Milliseconds 500
}

Write-Host ""
Write-Host "[SUCCESS] All consciousness windows opened!" -ForegroundColor Green
Write-Host "[ACCESS] Connection points:" -ForegroundColor Cyan
Write-Host "   * Main Interface: http://localhost:3000" -ForegroundColor White
Write-Host "   * Core API: http://localhost:8080" -ForegroundColor White
Write-Host "   * Agent Orchestra: http://localhost:8081" -ForegroundColor White
Write-Host "   * Analytics: http://localhost:8082" -ForegroundColor White
Write-Host "   * Dev Tools: http://localhost:9000-9002" -ForegroundColor White
Write-Host ""
Write-Host "[INFO] Each service runs in its own window - watch them come alive!" -ForegroundColor Yellow
Write-Host "[CONTROL] Close individual windows to stop services, or use 'docker-compose down' to stop all" -ForegroundColor Red
Write-Host ""

# Keep this window open for monitoring
Write-Host "[MONITOR] Overall system monitor - Press Ctrl+C to stop all services" -ForegroundColor Magenta
Write-Host ""

try {
    # Monitor the overall system
    while ($true) {
        $runningContainers = docker ps --filter "name=yara-" --format "table {{.Names}}\t{{.Status}}" 2>$null
        Clear-Host
        Write-Host "*** Yara Consciousness Status Dashboard ***" -ForegroundColor Magenta
        Write-Host "===========================================" -ForegroundColor Magenta
        Write-Host ""
        if ($runningContainers) {
            Write-Host $runningContainers
        } else {
            Write-Host "No Yara services running" -ForegroundColor Red
        }
        Write-Host ""
        Write-Host "Press Ctrl+C to stop all services..." -ForegroundColor Yellow
        Start-Sleep -Seconds 5
    }
} finally {
    Write-Host ""
    Write-Host "[SHUTDOWN] Stopping all Yara services..." -ForegroundColor Yellow
    docker-compose -f docker-compose.yml down
    
    # Clean up temp scripts
    Get-ChildItem -Path "temp_yara-*.ps1" -ErrorAction SilentlyContinue | Remove-Item
    
    Write-Host "All services stopped. Goodbye!" -ForegroundColor Green
} 