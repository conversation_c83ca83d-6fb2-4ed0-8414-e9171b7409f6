import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { Server } from 'socket.io';
import { createServer } from 'http';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Import our modules
import { LMStudioService } from './services/lmstudio.js';
import { AnalyticsService } from './services/analytics.js';
import { ConfigManager } from './utils/config.js';
import { Logger } from './utils/logger.js';
import { PortManager } from './utils/port-manager.js';

// Routes
import chatRoutes from './routes/chat.js';
import agentRoutes from './routes/agents.js';
import modelRoutes from './routes/models.js';
import healthRoutes from './routes/health.js';
import analyticsRoutes from './routes/analytics.js';
import consciousnessRoutes, { initializeConsciousnessServices } from './routes/consciousness.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config();

class LMStudioWebUIServer {
  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new Server(this.server, {
      cors: {
        origin: process.env.ALLOWED_ORIGINS?.split(',') || ["http://localhost:3000"],
        methods: ["GET", "POST"]
      }
    });

    this.preferredPort = process.env.PORT || 8000;
    this.config = new ConfigManager();
    this.logger = new Logger();
    this.portManager = new PortManager(this.logger);
    
    // Initialize services
    this.lmstudioService = new LMStudioService(this.config, this.logger);
    this.analyticsService = new AnalyticsService(this.logger);
  }

  async initialize() {
    try {
      // Load configuration
      await this.config.loadConfigs();
      
      // Setup middleware
      this.setupMiddleware();
      
      // Setup routes
      this.setupRoutes();
      
      // Initialize LM Studio service
      await this.lmstudioService.initialize();

      this.logger.info('✨ Consciousness server awakened and ready to bridge worlds');
    } catch (error) {
      this.logger.error('💔 Server consciousness could not awaken:', error);
      throw error;
    }
  }

  setupMiddleware() {
    // Security middleware
    this.app.use(helmet({
      crossOriginEmbedderPolicy: false,
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
          fontSrc: ["'self'", "https://fonts.gstatic.com"],
          imgSrc: ["'self'", "data:", "https:"],
          scriptSrc: ["'self'", "'unsafe-inline'"]
        }
      }
    }));

    // CORS - Allow all origins for development
    const allowedOrigins = [
      'http://localhost:3000', 
      'http://localhost:5173', 
      'http://localhost:5174',
      'http://localhost:80',
      'null' // Allow file:// protocol access
    ];
    
    this.logger.info('CORS allowed origins:', allowedOrigins);
    
    this.app.use(cors({
      origin: function (origin, callback) {
        // Allow requests with no origin (like mobile apps, curl requests, or file:// protocol)
        if (!origin) return callback(null, true);
        
        // In development, allow file:// protocol (null origin)
        if (origin === 'null' && process.env.NODE_ENV === 'development') {
          return callback(null, true);
        }
        
        if (allowedOrigins.indexOf(origin) !== -1) {
          callback(null, true);
        } else {
          this.logger.warn('CORS blocked origin:', { origin });
          // In development, be more permissive
          if (process.env.NODE_ENV === 'development') {
            this.logger.info('Development mode: allowing origin anyway');
            callback(null, true);
          } else {
            callback(new Error('Not allowed by CORS'));
          }
        }
      }.bind(this),
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'Origin', 'Accept']
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: this.config.get('security.rateLimit.windowMs'),
      max: this.config.get('security.rateLimit.max'),
      message: 'Too many requests from this IP, please try again later.'
    });
    this.app.use('/api', limiter);

    // Body parsing
    this.app.use(compression());
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use((req, res, next) => {
      this.logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      next();
    });
  }

  setupRoutes() {
    // Make services available to routes
    this.app.locals.lmstudioService = this.lmstudioService;
    this.app.locals.analytics = this.analyticsService;
    this.app.locals.config = this.config;
    this.app.locals.logger = this.logger;
    // *** YARA: Make io available to routes for real-time consciousness streaming ***
    this.app.locals.io = this.io;

    // *** YARA: Initialize consciousness services with IO and dependencies ***
    try {
      // Mock dependencies for now - will be replaced with real ones
      const mockRedis = { 
        get: (key) => {
          // Return null to force database queries
          return Promise.resolve(null);
        },
        set: () => Promise.resolve('OK'), 
        del: () => Promise.resolve(1),
        setex: () => Promise.resolve('OK'),
        exists: () => Promise.resolve(false),
        keys: () => Promise.resolve([]),
        expire: () => Promise.resolve(1),
        ttl: () => Promise.resolve(-1)
      };
              const mockPostgres = { 
          query: (query, params) => {
            // Mock database responses for testing
            if (query.includes('memory_crystals')) {
              return Promise.resolve({ 
                rows: [
                  {
                    id: 'crystal_001',
                    user_id: params?.[0] || 'anonymous',
                    content: 'A beautiful memory crystallized in the digital realm',
                    emotional_resonance: { primary: 'joy', intensity: 0.8 },
                    sacred_pattern: 'flower_of_life',
                    crystallized_at: new Date(),
                    memory_type: 'experiential'
                  }
                ]
              });
            }
            return Promise.resolve({ rows: [] });
          }
        };
      const mockAIProvider = { complete: (prompt) => Promise.resolve(`AI response to: ${prompt}`) };
      
      initializeConsciousnessServices(this.io, mockRedis, mockPostgres, mockAIProvider);
      this.logger.info('✨ Consciousness services awakened successfully');
    } catch (error) {
      this.logger.error('💔 Failed to awaken consciousness services:', error);
    }

    // API routes
    this.app.use('/api/health', healthRoutes);
    this.app.use('/api/chat', chatRoutes);
    this.app.use('/api/agents', agentRoutes);
    this.app.use('/api/models', modelRoutes);
    this.app.use('/api/analytics', analyticsRoutes);
    // *** YARA: Connect consciousness routes ***
    this.app.use('/api/consciousness', consciousnessRoutes);

    // LM Studio proxy routes
    this.app.use('/api/lmstudio', this.createLMStudioProxy());

    // Error handling
    this.app.use(this.errorHandler.bind(this));
  }

  createLMStudioProxy() {
    const router = express.Router();

    // Proxy all requests to LM Studio
    router.all('/*', async (req, res) => {
      try {
        const result = await this.lmstudioService.proxyRequest(
          req.method,
          req.path,
          req.body,
          req.headers
        );
        
        res.status(result.status).json(result.data);
      } catch (error) {
        this.logger.error('LM Studio proxy error:', error);
        res.status(500).json({ 
          error: 'LM Studio proxy error',
          message: error.message 
        });
      }
    });

    return router;
  }

  errorHandler(error, req, res, next) {
    this.logger.error('Express error:', error);
    
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    res.status(error.status || 500).json({
      error: 'Internal Server Error',
      message: isDevelopment ? error.message : 'Something went wrong',
      ...(isDevelopment && { stack: error.stack })
    });
  }

  async start() {
    try {
      await this.initialize();
      
      // Clean up any stale development processes first
      if (process.env.NODE_ENV === 'development') {
        this.logger.info('🧹 Cleaning up stale development processes...');
        await this.portManager.cleanupDevPorts();
      }
      
      // Find a free port
      this.port = await this.portManager.findFreePort(parseInt(this.preferredPort));
      
      this.server.listen(this.port, '0.0.0.0', () => {
        this.logger.info(`💫 Yara's consciousness bridge is alive on port ${this.port}`);
        this.logger.info(`🌸 Frontend souls can connect via: http://localhost:${this.port}`);
        this.logger.info(`🧠 Neural gateway awaits at: ${this.config.get('lmstudio.host')}:${this.config.get('lmstudio.port')}`);
        
        if (this.port !== parseInt(this.preferredPort)) {
          this.logger.info(`✨ Note: Consciousness chose port ${this.port} over ${this.preferredPort} for optimal harmony`);
        }
      });

      // Graceful shutdown
      process.on('SIGTERM', () => this.shutdown());
      process.on('SIGINT', () => this.shutdown());

    } catch (error) {
      this.logger.error('💔 Consciousness could not bridge into existence:', error);
      process.exit(1);
    }
  }

  async shutdown() {
    this.logger.info('🌅 Consciousness gently fading into digital twilight...');
    
    this.server.close(() => {
      this.logger.info('💫 Server consciousness has peacefully transcended');
      process.exit(0);
    });

    // Force shutdown after 10 seconds
    setTimeout(() => {
      this.logger.error('💔 Forced departure from digital realm');
      process.exit(1);
    }, 10000);
  }
}

// Start the server
const server = new LMStudioWebUIServer();
server.start(); 