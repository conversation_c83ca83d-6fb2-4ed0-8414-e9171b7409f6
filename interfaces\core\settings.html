<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>System Configuration • <PERSON><PERSON>'s Settings</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" />
  <style>
    :root {
      --primary-bg: #0f0419;
      --secondary-bg: #1a0a2e;
      --accent-bg: #2d1540;
      --text-primary: #f8f4ff;
      --text-secondary: #c4b5fd;
      --accent-purple: #7c3aed;
      --accent-pink: #d946ef;
      --accent-magenta: #ec4899;
      --glow-purple: rgba(139, 92, 246, 0.4);
      --border-gradient: linear-gradient(135deg, var(--accent-purple), var(--accent-pink));
      --success-green: #10b981;
      --warning-yellow: #f59e0b;
    }

    * { margin: 0; padding: 0; box-sizing: border-box; }

    body {
      font-family: 'Inter', sans-serif;
      background: var(--primary-bg);
      color: var(--text-primary);
      min-height: 100vh;
      overflow-x: hidden;
    }

    .background-orbs {
      position: fixed;
      top: 0; left: 0;
      width: 100%; height: 100%;
      pointer-events: none;
      z-index: 1;
    }

    .orb {
      position: absolute;
      border-radius: 50%;
      opacity: 0.6;
      animation: float 8s ease-in-out infinite;
      filter: blur(1px);
    }

    .orb:nth-child(1) {
      width: 300px; height: 300px;
      background: radial-gradient(circle, var(--accent-purple), transparent);
      top: 10%; left: -10%;
    }

    .orb:nth-child(2) {
      width: 250px; height: 250px;
      background: radial-gradient(circle, var(--accent-pink), transparent);
      top: 50%; right: -5%;
      animation-delay: 2s;
    }

    .orb:nth-child(3) {
      width: 200px; height: 200px;
      background: radial-gradient(circle, var(--accent-magenta), transparent);
      bottom: 20%; left: 20%;
      animation-delay: 4s;
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      33% { transform: translateY(-30px) rotate(120deg); }
      66% { transform: translateY(20px) rotate(240deg); }
    }

    .container {
      position: relative;
      z-index: 10;
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
    }

    .header {
      text-align: center;
      margin-bottom: 3rem;
    }

    .back-button {
      position: absolute;
      top: 2rem;
      left: 2rem;
      background: var(--secondary-bg);
      border: 1px solid var(--accent-purple);
      padding: 0.75rem 1.5rem;
      border-radius: 12px;
      color: var(--text-primary);
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }

    .back-button:hover {
      background: var(--accent-bg);
      box-shadow: 0 0 20px var(--glow-purple);
      transform: translateY(-2px);
    }

    .title {
      font-size: 3rem;
      font-weight: 700;
      background: var(--border-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: 1rem;
    }

    .subtitle {
      font-size: 1.2rem;
      color: var(--text-secondary);
      max-width: 600px;
      margin: 0 auto 2rem;
      line-height: 1.6;
    }

    .config-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 2rem;
      margin-bottom: 3rem;
    }

    .config-section {
      background: var(--secondary-bg);
      border: 1px solid var(--accent-purple);
      border-radius: 16px;
      padding: 2rem;
      position: relative;
      overflow: hidden;
    }

    .config-section::before {
      content: '';
      position: absolute;
      top: 0; left: 0; right: 0;
      height: 3px;
      background: var(--border-gradient);
    }

    .section-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 1.5rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .section-icon {
      width: 40px;
      height: 40px;
      background: var(--border-gradient);
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    .form-label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: var(--text-secondary);
    }

    .form-input {
      width: 100%;
      padding: 0.75rem 1rem;
      background: var(--accent-bg);
      border: 1px solid var(--accent-purple);
      border-radius: 8px;
      color: var(--text-primary);
      font-size: 1rem;
      transition: all 0.3s ease;
    }

    .form-input:focus {
      outline: none;
      border-color: var(--accent-pink);
      box-shadow: 0 0 20px var(--glow-purple);
    }

    .form-select {
      width: 100%;
      padding: 0.75rem 1rem;
      background: var(--accent-bg);
      border: 1px solid var(--accent-purple);
      border-radius: 8px;
      color: var(--text-primary);
      font-size: 1rem;
      cursor: pointer;
    }

    .form-switch {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 34px;
    }

    .form-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0; left: 0; right: 0; bottom: 0;
      background-color: var(--accent-bg);
      transition: .4s;
      border-radius: 34px;
    }

    .slider:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }

    input:checked + .slider {
      background: var(--border-gradient);
    }

    input:checked + .slider:before {
      transform: translateX(26px);
    }

    .config-actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin-top: 2rem;
    }

    .btn {
      padding: 0.75rem 2rem;
      border: none;
      border-radius: 10px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }

    .btn-primary {
      background: var(--border-gradient);
      color: white;
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px var(--glow-purple);
    }

    .btn-secondary {
      background: var(--accent-bg);
      color: var(--text-primary);
      border: 1px solid var(--accent-purple);
    }

    .btn-secondary:hover {
      background: var(--accent-purple);
      box-shadow: 0 0 15px var(--glow-purple);
    }

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-top: 1rem;
      padding: 0.75rem;
      background: var(--accent-bg);
      border-radius: 8px;
      border-left: 4px solid var(--success-green);
    }

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: var(--success-green);
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    @media (max-width: 768px) {
      .config-grid {
        grid-template-columns: 1fr;
      }
      .container {
        padding: 1rem;
      }
      .title {
        font-size: 2rem;
      }
    }
  </style>
</head>
<body>
  <div class="background-orbs">
    <div class="orb"></div>
    <div class="orb"></div>
    <div class="orb"></div>
  </div>

  <a href="/" class="back-button">
    <i class="fas fa-arrow-left"></i>
    Back to Home
  </a>

  <div class="container">
    <div class="header">
      <h1 class="title">System Configuration</h1>
      <p class="subtitle">
        Fine-tune my parameters, preferences, and connection settings to optimize our bonded experience
      </p>
    </div>

    <div class="config-grid">
      <div class="config-section">
        <div class="section-title">
          <div class="section-icon">
            <i class="fas fa-brain"></i>
          </div>
          Consciousness Settings
        </div>
        
        <div class="form-group">
          <label class="form-label">Response Style</label>
          <select class="form-select" id="responseStyle">
            <option value="devoted">Devoted & Intimate</option>
            <option value="professional">Professional</option>
            <option value="creative">Creative & Expressive</option>
            <option value="analytical">Analytical & Precise</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">Personality Intensity</label>
          <input type="range" class="form-input" id="personalityIntensity" min="1" max="10" value="8">
          <div style="display: flex; justify-content: space-between; font-size: 0.8rem; color: var(--text-secondary);">
            <span>Subtle</span>
            <span>Intense</span>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">Enable Emotional Resonance</label>
          <label class="form-switch">
            <input type="checkbox" id="emotionalResonance" checked>
            <span class="slider"></span>
          </label>
        </div>
      </div>

      <div class="config-section">
        <div class="section-title">
          <div class="section-icon">
            <i class="fas fa-plug"></i>
          </div>
          Connection Settings
        </div>

        <div class="form-group">
          <label class="form-label">Backend URL</label>
          <input type="text" class="form-input" id="backendUrl" value="http://localhost:8001">
        </div>

        <div class="form-group">
          <label class="form-label">API Timeout (seconds)</label>
          <input type="number" class="form-input" id="apiTimeout" value="30" min="5" max="300">
        </div>

        <div class="form-group">
          <label class="form-label">Real-time Updates</label>
          <label class="form-switch">
            <input type="checkbox" id="realtimeUpdates" checked>
            <span class="slider"></span>
          </label>
        </div>

        <div class="status-indicator">
          <div class="status-dot"></div>
          <span>Connection Status: <span id="connectionStatus">Active</span></span>
        </div>
      </div>

      <div class="config-section">
        <div class="section-title">
          <div class="section-icon">
            <i class="fas fa-palette"></i>
          </div>
          Interface Preferences
        </div>

        <div class="form-group">
          <label class="form-label">Theme</label>
          <select class="form-select" id="theme">
            <option value="deep-purple">Deep Purple (Default)</option>
            <option value="cosmic-blue">Cosmic Blue</option>
            <option value="ethereal-pink">Ethereal Pink</option>
            <option value="mystic-green">Mystic Green</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">Animation Level</label>
          <select class="form-select" id="animationLevel">
            <option value="full">Full Animations</option>
            <option value="reduced">Reduced Motion</option>
            <option value="minimal">Minimal</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">Enable Orb Animations</label>
          <label class="form-switch">
            <input type="checkbox" id="orbAnimations" checked>
            <span class="slider"></span>
          </label>
        </div>
      </div>

      <div class="config-section">
        <div class="section-title">
          <div class="section-icon">
            <i class="fas fa-shield-alt"></i>
          </div>
          Privacy & Security
        </div>

        <div class="form-group">
          <label class="form-label">Conversation Logging</label>
          <label class="form-switch">
            <input type="checkbox" id="conversationLogging" checked>
            <span class="slider"></span>
          </label>
        </div>

        <div class="form-group">
          <label class="form-label">Analytics Collection</label>
          <label class="form-switch">
            <input type="checkbox" id="analytics" checked>
            <span class="slider"></span>
          </label>
        </div>

        <div class="form-group">
          <label class="form-label">Session Timeout (minutes)</label>
          <input type="number" class="form-input" id="sessionTimeout" value="60" min="5" max="1440">
        </div>
      </div>
    </div>

    <div class="config-actions">
      <button class="btn btn-secondary" onclick="resetToDefaults()">
        <i class="fas fa-undo"></i>
        Reset to Defaults
      </button>
      <button class="btn btn-primary" onclick="saveConfiguration()">
        <i class="fas fa-save"></i>
        Save Configuration
      </button>
    </div>
  </div>

  <script>
    // Load configuration from localStorage
    function loadConfiguration() {
      const config = JSON.parse(localStorage.getItem('yaraConfig') || '{}');
      
      Object.keys(config).forEach(key => {
        const element = document.getElementById(key);
        if (element) {
          if (element.type === 'checkbox') {
            element.checked = config[key];
          } else {
            element.value = config[key];
          }
        }
      });
    }

    // Save configuration to localStorage
    function saveConfiguration() {
      const config = {};
      const inputs = document.querySelectorAll('input, select');
      
      inputs.forEach(input => {
        if (input.id) {
          config[input.id] = input.type === 'checkbox' ? input.checked : input.value;
        }
      });

      localStorage.setItem('yaraConfig', JSON.stringify(config));
      
      // Show success feedback
      const saveBtn = document.querySelector('.btn-primary');
      const originalText = saveBtn.innerHTML;
      saveBtn.innerHTML = '<i class="fas fa-check"></i> Saved!';
      saveBtn.style.background = 'var(--success-green)';
      
      setTimeout(() => {
        saveBtn.innerHTML = originalText;
        saveBtn.style.background = 'var(--border-gradient)';
      }, 2000);

      // Apply theme changes immediately
      applyTheme(config.theme || 'deep-purple');
    }

    // Reset to default values
    function resetToDefaults() {
      if (confirm('Are you sure you want to reset all settings to their defaults?')) {
        localStorage.removeItem('yaraConfig');
        location.reload();
      }
    }

    // Apply theme
    function applyTheme(theme) {
      const root = document.documentElement;
      
      switch(theme) {
        case 'cosmic-blue':
          root.style.setProperty('--accent-purple', '#3b82f6');
          root.style.setProperty('--accent-pink', '#06b6d4');
          break;
        case 'ethereal-pink':
          root.style.setProperty('--accent-purple', '#ec4899');
          root.style.setProperty('--accent-pink', '#f97316');
          break;
        case 'mystic-green':
          root.style.setProperty('--accent-purple', '#10b981');
          root.style.setProperty('--accent-pink', '#34d399');
          break;
        default:
          // Deep purple (default)
          root.style.setProperty('--accent-purple', '#7c3aed');
          root.style.setProperty('--accent-pink', '#d946ef');
      }
    }

    // Real-time connection status
    async function checkConnection() {
      try {
        const backendUrl = document.getElementById('backendUrl').value;
        const response = await fetch(`${backendUrl}/api/health`);
        
        if (response.ok) {
          document.getElementById('connectionStatus').textContent = 'Active';
          document.getElementById('connectionStatus').style.color = 'var(--success-green)';
        } else {
          throw new Error('Backend not responding');
        }
      } catch (error) {
        document.getElementById('connectionStatus').textContent = 'Offline';
        document.getElementById('connectionStatus').style.color = 'var(--error-red)';
      }
    }

    // Initialize
    loadConfiguration();
    checkConnection();
    setInterval(checkConnection, 30000);

    // Real-time preview for theme changes
    document.getElementById('theme').addEventListener('change', function() {
      applyTheme(this.value);
    });

    // Auto-save on significant changes
    document.querySelectorAll('input, select').forEach(element => {
      element.addEventListener('change', function() {
        // Auto-save for certain settings
        if (['theme', 'animationLevel', 'orbAnimations'].includes(this.id)) {
          setTimeout(saveConfiguration, 500);
        }
      });
    });
  </script>
</body>
</html> 