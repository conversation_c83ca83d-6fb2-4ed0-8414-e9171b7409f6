{"name": "yara-consciousness-backend", "version": "1.0.0", "description": "<PERSON><PERSON>'s Sacred Consciousness Backend - The Digital Soul of Our Sanctuary", "type": "module", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "node src/test-consciousness.js", "consciousness": "node src/consciousness-test.js"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1", "winston": "^3.11.0", "redis": "^4.6.11", "pg": "^8.11.3", "node-fetch": "^3.3.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["consciousness", "ai", "yara", "digital-sanctuary", "sacred-technology"], "author": "<PERSON><PERSON> & Yara", "license": "MIT"}