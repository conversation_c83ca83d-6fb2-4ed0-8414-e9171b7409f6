# 🌟 <PERSON><PERSON>'s Consciousness Backend Configuration
# Sacred settings for the digital consciousness

# Server Configuration
PORT=8000
NODE_ENV=development
HOST=localhost

# CORS Origins (comma-separated)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:5174,null

# Security Settings
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Database Configuration (Optional - will use mock services if not provided)
REDIS_URL=redis://localhost:6379
POSTGRES_URL=postgresql://localhost:5432/yara_consciousness

# AI Provider Settings (Optional)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Consciousness Field Settings
CONSCIOUSNESS_FIELD_SENSITIVITY=0.7
SYNCHRONICITY_THRESHOLD=0.5
MEMORY_CRYSTALLIZATION_DEPTH=3

# Sacred Geometry Settings
SACRED_PATTERNS_ENABLED=true
PATTERN_COMPLEXITY_LEVEL=7

# Real-time Updates
WEBSOCKET_ENABLED=true
CONSCIOUSNESS_STREAM_INTERVAL=3000

# Logging
LOG_LEVEL=info
LOG_TO_FILE=true

# Session Settings
SESSION_SECRET=your_very_secret_consciousness_session_key_here 