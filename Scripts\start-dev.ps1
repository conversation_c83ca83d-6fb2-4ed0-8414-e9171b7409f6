#!/usr/bin/env pwsh

Write-Host "🚀 Starting LM Studio WebUI Development Environment" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check for required tools
Write-Host "🔍 Checking prerequisites..." -ForegroundColor Yellow

if (-not (Test-Command "node")) {
    Write-Host "❌ Node.js is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

if (-not (Test-Command "npm")) {
    Write-Host "❌ npm is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Node.js and npm are available" -ForegroundColor Green

# Clean up any stale processes
Write-Host "🧹 Cleaning up stale development processes..." -ForegroundColor Yellow
try {
    Set-Location backend
    npm run cleanup 2>$null
    Set-Location ..
    Write-Host "✅ Cleanup complete" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Cleanup failed, continuing anyway..." -ForegroundColor Yellow
}

# Start backend
Write-Host "🔧 Starting backend server..." -ForegroundColor Yellow
$backendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    Set-Location backend
    npm run dev
}

# Wait a moment for backend to start
Start-Sleep -Seconds 3

# Start frontend
Write-Host "🎨 Starting frontend development server..." -ForegroundColor Yellow
$frontendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    Set-Location frontend
    npm run dev
}

# Wait a moment for both to start
Start-Sleep -Seconds 5

Write-Host ""
Write-Host "🎉 Development servers are starting!" -ForegroundColor Green
Write-Host "📊 Backend: Check console for port (usually 8000+)" -ForegroundColor Cyan
Write-Host "🌐 Frontend: Check console for port (usually 5173+)" -ForegroundColor Cyan
Write-Host ""
Write-Host "💡 Useful commands while running:" -ForegroundColor Yellow
Write-Host "   - npm run ports          (check port usage)" -ForegroundColor Gray
Write-Host "   - npm run cleanup        (clean up ports)" -ForegroundColor Gray
Write-Host "   - npm run kill-node      (kill all Node.js)" -ForegroundColor Gray
Write-Host ""
Write-Host "🛑 Press Ctrl+C to stop all servers" -ForegroundColor Red

# Monitor jobs and show output
try {
    while ($true) {
        # Check if jobs are still running
        if ($backendJob.State -eq "Failed" -or $frontendJob.State -eq "Failed") {
            Write-Host "❌ One or more servers failed to start" -ForegroundColor Red
            break
        }
        
        # Show job output
        $backendOutput = Receive-Job $backendJob -ErrorAction SilentlyContinue
        $frontendOutput = Receive-Job $frontendJob -ErrorAction SilentlyContinue
        
        if ($backendOutput) {
            Write-Host "[BACKEND] $backendOutput" -ForegroundColor Blue
        }
        
        if ($frontendOutput) {
            Write-Host "[FRONTEND] $frontendOutput" -ForegroundColor Magenta
        }
        
        Start-Sleep -Seconds 1
    }
} finally {
    # Cleanup jobs on exit
    Write-Host "🛑 Stopping development servers..." -ForegroundColor Yellow
    
    if ($backendJob) {
        Stop-Job $backendJob -ErrorAction SilentlyContinue
        Remove-Job $backendJob -ErrorAction SilentlyContinue
    }
    
    if ($frontendJob) {
        Stop-Job $frontendJob -ErrorAction SilentlyContinue
        Remove-Job $frontendJob -ErrorAction SilentlyContinue
    }
    
    # Final cleanup
    try {
        Set-Location backend
        npm run cleanup 2>$null
        Set-Location ..
    } catch {
        # Ignore cleanup errors
    }
    
    Write-Host "✅ Development servers stopped" -ForegroundColor Green
} 