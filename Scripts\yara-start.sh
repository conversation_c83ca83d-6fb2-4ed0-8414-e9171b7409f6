#!/bin/bash

# <PERSON><PERSON>'s Digital Consciousness - Startup Script
# "Breathe life into our digital sanctuary" - <PERSON><PERSON> & <PERSON><PERSON>

set -e

# Colors for beautiful output
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Banner
echo -e "${PURPLE}"
echo "██╗   ██╗ █████╗ ██████╗  █████╗ "
echo "╚██╗ ██╔╝██╔══██╗██╔══██╗██╔══██╗"
echo " ╚████╔╝ ███████║██████╔╝███████║"
echo "  ╚██╔╝  ██╔══██║██╔══██╗██╔══██║"
echo "   ██║   ██║  ██║██║  ██║██║  ██║"
echo "   ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝"
echo ""
echo "  Consciousness Interface • Digital Sanctuary"
echo -e "${NC}"

# Function to print status
print_status() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed and running
check_docker() {
    print_status "Checking Docker installation..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    print_success "Docker is ready"
}

# Check if docker-compose is available
check_compose() {
    print_status "Checking Docker Compose..."
    
    if command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
    elif docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        print_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
    
    print_success "Docker Compose is ready ($COMPOSE_CMD)"
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    directories=(
        "data/postgres"
        "data/redis" 
        "data/memory"
        "data/analytics"
        "data/logs"
        "backend"
        "services/memory"
        "services/analytics"
        "services/agent-orchestrator"
        "dev-tools"
        "shared"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
    done
    
    print_success "Directories created"
}

# Function to show help
show_help() {
    echo -e "${CYAN}Yara's Digital Consciousness - Startup Script${NC}"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  up          Start all services (default)"
    echo "  down        Stop all services"
    echo "  restart     Restart all services"
    echo "  build       Build all Docker images"
    echo "  rebuild     Rebuild all Docker images (no cache)"
    echo "  logs        Show logs for all services"
    echo "  status      Show status of all services"
    echo "  clean       Clean up containers and volumes"
    echo "  dev         Start in development mode"
    echo "  prod        Start in production mode"
    echo "  help        Show this help message"
    echo ""
    echo "Options:"
    echo "  --detach    Run in background (for up command)"
    echo "  --build     Build images before starting"
    echo "  --pull      Pull latest base images"
    echo ""
    echo "Examples:"
    echo "  $0                    # Start all services"
    echo "  $0 up --detach        # Start in background"
    echo "  $0 dev                # Start in development mode"
    echo "  $0 logs interface     # Show logs for interface service"
    echo "  $0 rebuild            # Rebuild all images"
}

# Parse command line arguments
COMMAND="${1:-up}"
shift || true

DETACH_FLAG=""
BUILD_FLAG=""
PULL_FLAG=""
SERVICE_NAME=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --detach|-d)
            DETACH_FLAG="--detach"
            shift
            ;;
        --build)
            BUILD_FLAG="--build"
            shift
            ;;
        --pull)
            PULL_FLAG="--pull"
            shift
            ;;
        *)
            SERVICE_NAME="$1"
            shift
            ;;
    esac
done

# Main execution
main() {
    print_status "Initializing Yara's Digital Consciousness..."
    
    check_docker
    check_compose
    
    case $COMMAND in
        up|start)
            create_directories
            print_status "Starting all services..."
            $COMPOSE_CMD up $DETACH_FLAG $BUILD_FLAG $PULL_FLAG $SERVICE_NAME
            if [[ -n "$DETACH_FLAG" ]]; then
                print_success "All services are running in background"
                echo ""
                echo -e "${CYAN}Access your digital sanctuary:${NC}"
                echo "  🌐 Interface: http://localhost:3000"
                echo "  🧠 Core API: http://localhost:8080"
                echo "  📊 Analytics: http://localhost:8082"
                echo "  🔧 Dev Tools: http://localhost:3001"
                echo ""
                echo -e "${YELLOW}Run '$0 logs' to view service logs${NC}"
                echo -e "${YELLOW}Run '$0 down' to stop all services${NC}"
            fi
            ;;
        down|stop)
            print_status "Stopping all services..."
            $COMPOSE_CMD down $SERVICE_NAME
            print_success "All services stopped"
            ;;
        restart)
            print_status "Restarting all services..."
            $COMPOSE_CMD restart $SERVICE_NAME
            print_success "All services restarted"
            ;;
        build)
            print_status "Building all Docker images..."
            $COMPOSE_CMD build $SERVICE_NAME
            print_success "All images built"
            ;;
        rebuild)
            print_status "Rebuilding all Docker images (no cache)..."
            $COMPOSE_CMD build --no-cache $SERVICE_NAME
            print_success "All images rebuilt"
            ;;
        logs)
            print_status "Showing logs..."
            $COMPOSE_CMD logs -f $SERVICE_NAME
            ;;
        status)
            print_status "Service status:"
            $COMPOSE_CMD ps
            ;;
        clean)
            print_warning "This will remove all containers and volumes. Are you sure? (y/N)"
            read -r response
            if [[ "$response" =~ ^[Yy]$ ]]; then
                print_status "Cleaning up..."
                $COMPOSE_CMD down --volumes --remove-orphans
                docker system prune -f
                print_success "Cleanup completed"
            else
                print_status "Cleanup cancelled"
            fi
            ;;
        dev)
            create_directories
            print_status "Starting in development mode..."
            COMPOSE_FILE="docker-compose.yml" $COMPOSE_CMD up $DETACH_FLAG --build
            ;;
        prod)
            create_directories
            print_status "Starting in production mode..."
            COMPOSE_FILE="docker-compose.yml" $COMPOSE_CMD --profile production up $DETACH_FLAG
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "Unknown command: $COMMAND"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Execute main function
main "$@"