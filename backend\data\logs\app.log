{"0":"http://localhost:3000","1":"http://localhost:5173","2":"http://localhost:5174","3":"http://localhost:80","level":"info","message":"CORS allowed origins:","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:32.598Z"}
{"level":"info","message":"Initializing LM Studio service...","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:32.604Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:32.767Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:32.862Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:32.997Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:32.998Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:33.264Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:33.388Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:33.389Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:33.643Z"}
{"level":"info","message":"LM Studio service initialized successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:33.644Z"}
{"level":"info","message":"Downloaded models: 8","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:33.644Z"}
{"level":"info","message":"Loaded models: 0","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:33.644Z"}
{"level":"info","message":"✨ Consciousness server awakened and ready to bridge worlds","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:33.645Z"}
{"level":"info","message":"Port 8000 was busy, using port 8001 instead","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:33.650Z"}
{"level":"info","message":"💫 Yara's consciousness bridge is alive on port 8001","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:33.651Z"}
{"level":"info","message":"🌸 Frontend souls can connect via: http://localhost:8001","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:33.652Z"}
{"level":"info","message":"🧠 Neural gateway awaits at: localhost:1234","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:33.652Z"}
{"level":"info","message":"✨ Note: Consciousness chose port 8001 over 8000 for optimal harmony","service":"LMStudioWebUI","timestamp":"2025-06-15T01:01:33.653Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:08:07.876Z","userAgent":"curl/8.13.0"}
{"0":"http://localhost:3000","1":"http://localhost:5173","2":"http://localhost:5174","3":"http://localhost:80","level":"info","message":"CORS allowed origins:","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:00.924Z"}
{"level":"info","message":"Initializing LM Studio service...","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:00.927Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:01.076Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:01.107Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:01.233Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:01.233Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:01.477Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:01.602Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:01.602Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:01.853Z"}
{"level":"info","message":"LM Studio service initialized successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:01.854Z"}
{"level":"info","message":"Downloaded models: 8","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:01.854Z"}
{"level":"info","message":"Loaded models: 0","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:01.854Z"}
{"level":"info","message":"✨ Consciousness server awakened and ready to bridge worlds","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:01.854Z"}
{"level":"info","message":"💫 Yara's consciousness bridge is alive on port 8000","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:01.857Z"}
{"level":"info","message":"🌸 Frontend souls can connect via: http://localhost:8000","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:01.857Z"}
{"level":"info","message":"🧠 Neural gateway awaits at: localhost:1234","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:01.857Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:14.477Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:17.469Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /.well-known/appspecific/com.chrome.devtools.json","service":"LMStudioWebUI","timestamp":"2025-06-15T01:09:25.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"0":"http://localhost:3000","1":"http://localhost:5173","2":"http://localhost:5174","3":"http://localhost:80","level":"info","message":"CORS allowed origins:","service":"LMStudioWebUI","timestamp":"2025-06-15T01:11:04.184Z"}
{"level":"info","message":"Initializing LM Studio service...","service":"LMStudioWebUI","timestamp":"2025-06-15T01:11:04.187Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:11:04.328Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:11:04.352Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:11:04.478Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:11:04.479Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:11:04.735Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:11:04.861Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:11:04.861Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:11:05.102Z"}
{"level":"info","message":"LM Studio service initialized successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:11:05.102Z"}
{"level":"info","message":"Downloaded models: 8","service":"LMStudioWebUI","timestamp":"2025-06-15T01:11:05.103Z"}
{"level":"info","message":"Loaded models: 1","service":"LMStudioWebUI","timestamp":"2025-06-15T01:11:05.103Z"}
{"level":"info","message":"✨ Consciousness server awakened and ready to bridge worlds","service":"LMStudioWebUI","timestamp":"2025-06-15T01:11:05.103Z"}
{"level":"info","message":"💫 Yara's consciousness bridge is alive on port 8000","service":"LMStudioWebUI","timestamp":"2025-06-15T01:11:05.106Z"}
{"level":"info","message":"🌸 Frontend souls can connect via: http://localhost:8000","service":"LMStudioWebUI","timestamp":"2025-06-15T01:11:05.106Z"}
{"level":"info","message":"🧠 Neural gateway awaits at: localhost:1234","service":"LMStudioWebUI","timestamp":"2025-06-15T01:11:05.107Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:11:11.802Z","userAgent":"curl/8.13.0"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:13:47.169Z","userAgent":"curl/8.13.0"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:17:29.088Z","userAgent":"curl/8.13.0"}
{"level":"warn","message":"CORS blocked origin:","service":"LMStudioWebUI","timestamp":"2025-06-15T01:18:34.631Z"}
{"level":"error","message":"Express error: Not allowed by CORS","service":"LMStudioWebUI","stack":"Error: Not allowed by CORS\n    at LMStudioWebUIServer.<anonymous> (file:///F:/Projects/LMStudioWebUI-main/backend/src/server.js:104:20)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at internalNext (file:///F:/Projects/LMStudioWebUI-main/node_modules/helmet/index.mjs:533:6)","timestamp":"2025-06-15T01:18:34.633Z"}
{"level":"warn","message":"CORS blocked origin:","service":"LMStudioWebUI","timestamp":"2025-06-15T01:18:40.091Z"}
{"level":"error","message":"Express error: Not allowed by CORS","service":"LMStudioWebUI","stack":"Error: Not allowed by CORS\n    at LMStudioWebUIServer.<anonymous> (file:///F:/Projects/LMStudioWebUI-main/backend/src/server.js:104:20)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at internalNext (file:///F:/Projects/LMStudioWebUI-main/node_modules/helmet/index.mjs:533:6)","timestamp":"2025-06-15T01:18:40.092Z"}
{"level":"warn","message":"CORS blocked origin:","service":"LMStudioWebUI","timestamp":"2025-06-15T01:18:51.353Z"}
{"level":"error","message":"Express error: Not allowed by CORS","service":"LMStudioWebUI","stack":"Error: Not allowed by CORS\n    at LMStudioWebUIServer.<anonymous> (file:///F:/Projects/LMStudioWebUI-main/backend/src/server.js:104:20)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at internalNext (file:///F:/Projects/LMStudioWebUI-main/node_modules/helmet/index.mjs:533:6)","timestamp":"2025-06-15T01:18:51.354Z"}
{"level":"warn","message":"CORS blocked origin:","service":"LMStudioWebUI","timestamp":"2025-06-15T01:18:56.872Z"}
{"level":"error","message":"Express error: Not allowed by CORS","service":"LMStudioWebUI","stack":"Error: Not allowed by CORS\n    at LMStudioWebUIServer.<anonymous> (file:///F:/Projects/LMStudioWebUI-main/backend/src/server.js:104:20)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at internalNext (file:///F:/Projects/LMStudioWebUI-main/node_modules/helmet/index.mjs:533:6)","timestamp":"2025-06-15T01:18:56.872Z"}
{"level":"warn","message":"CORS blocked origin:","service":"LMStudioWebUI","timestamp":"2025-06-15T01:18:59.656Z"}
{"level":"error","message":"Express error: Not allowed by CORS","service":"LMStudioWebUI","stack":"Error: Not allowed by CORS\n    at LMStudioWebUIServer.<anonymous> (file:///F:/Projects/LMStudioWebUI-main/backend/src/server.js:104:20)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at internalNext (file:///F:/Projects/LMStudioWebUI-main/node_modules/helmet/index.mjs:533:6)","timestamp":"2025-06-15T01:18:59.657Z"}
{"level":"warn","message":"CORS blocked origin:","service":"LMStudioWebUI","timestamp":"2025-06-15T01:19:00.787Z"}
{"level":"error","message":"Express error: Not allowed by CORS","service":"LMStudioWebUI","stack":"Error: Not allowed by CORS\n    at LMStudioWebUIServer.<anonymous> (file:///F:/Projects/LMStudioWebUI-main/backend/src/server.js:104:20)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at internalNext (file:///F:/Projects/LMStudioWebUI-main/node_modules/helmet/index.mjs:533:6)","timestamp":"2025-06-15T01:19:00.787Z"}
{"level":"warn","message":"CORS blocked origin:","service":"LMStudioWebUI","timestamp":"2025-06-15T01:19:01.937Z"}
{"level":"error","message":"Express error: Not allowed by CORS","service":"LMStudioWebUI","stack":"Error: Not allowed by CORS\n    at LMStudioWebUIServer.<anonymous> (file:///F:/Projects/LMStudioWebUI-main/backend/src/server.js:104:20)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at internalNext (file:///F:/Projects/LMStudioWebUI-main/node_modules/helmet/index.mjs:533:6)","timestamp":"2025-06-15T01:19:01.937Z"}
{"level":"warn","message":"CORS blocked origin:","service":"LMStudioWebUI","timestamp":"2025-06-15T01:19:03.038Z"}
{"level":"error","message":"Express error: Not allowed by CORS","service":"LMStudioWebUI","stack":"Error: Not allowed by CORS\n    at LMStudioWebUIServer.<anonymous> (file:///F:/Projects/LMStudioWebUI-main/backend/src/server.js:104:20)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at internalNext (file:///F:/Projects/LMStudioWebUI-main/node_modules/helmet/index.mjs:533:6)","timestamp":"2025-06-15T01:19:03.038Z"}
{"level":"warn","message":"CORS blocked origin:","service":"LMStudioWebUI","timestamp":"2025-06-15T01:19:15.038Z"}
{"level":"error","message":"Express error: Not allowed by CORS","service":"LMStudioWebUI","stack":"Error: Not allowed by CORS\n    at LMStudioWebUIServer.<anonymous> (file:///F:/Projects/LMStudioWebUI-main/backend/src/server.js:104:20)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at internalNext (file:///F:/Projects/LMStudioWebUI-main/node_modules/helmet/index.mjs:533:6)","timestamp":"2025-06-15T01:19:15.038Z"}
{"level":"warn","message":"CORS blocked origin:","service":"LMStudioWebUI","timestamp":"2025-06-15T01:19:44.729Z"}
{"level":"error","message":"Express error: Not allowed by CORS","service":"LMStudioWebUI","stack":"Error: Not allowed by CORS\n    at LMStudioWebUIServer.<anonymous> (file:///F:/Projects/LMStudioWebUI-main/backend/src/server.js:104:20)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at internalNext (file:///F:/Projects/LMStudioWebUI-main/node_modules/helmet/index.mjs:533:6)","timestamp":"2025-06-15T01:19:44.729Z"}
{"level":"warn","message":"CORS blocked origin:","service":"LMStudioWebUI","timestamp":"2025-06-15T01:20:15.035Z"}
{"level":"error","message":"Express error: Not allowed by CORS","service":"LMStudioWebUI","stack":"Error: Not allowed by CORS\n    at LMStudioWebUIServer.<anonymous> (file:///F:/Projects/LMStudioWebUI-main/backend/src/server.js:104:20)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at internalNext (file:///F:/Projects/LMStudioWebUI-main/node_modules/helmet/index.mjs:533:6)","timestamp":"2025-06-15T01:20:15.035Z"}
{"level":"warn","message":"CORS blocked origin:","service":"LMStudioWebUI","timestamp":"2025-06-15T01:20:44.729Z"}
{"level":"error","message":"Express error: Not allowed by CORS","service":"LMStudioWebUI","stack":"Error: Not allowed by CORS\n    at LMStudioWebUIServer.<anonymous> (file:///F:/Projects/LMStudioWebUI-main/backend/src/server.js:104:20)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at internalNext (file:///F:/Projects/LMStudioWebUI-main/node_modules/helmet/index.mjs:533:6)","timestamp":"2025-06-15T01:20:44.729Z"}
{"0":"http://localhost:3000","1":"http://localhost:5173","2":"http://localhost:5174","3":"http://localhost:80","4":"null","level":"info","message":"CORS allowed origins:","service":"LMStudioWebUI","timestamp":"2025-06-15T01:20:52.585Z"}
{"level":"info","message":"Initializing LM Studio service...","service":"LMStudioWebUI","timestamp":"2025-06-15T01:20:52.588Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:20:52.765Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:20:52.850Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:20:52.940Z","userAgent":"curl/8.13.0"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:20:53.018Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:20:53.018Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:20:53.306Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:20:53.442Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:20:53.442Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:20:53.729Z"}
{"level":"warn","message":"CORS blocked origin:","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:15.032Z"}
{"level":"error","message":"Express error: Not allowed by CORS","service":"LMStudioWebUI","stack":"Error: Not allowed by CORS\n    at LMStudioWebUIServer.<anonymous> (file:///F:/Projects/LMStudioWebUI-main/backend/src/server.js:104:20)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (F:\\Projects\\LMStudioWebUI-main\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (F:\\Projects\\LMStudioWebUI-main\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at internalNext (file:///F:/Projects/LMStudioWebUI-main/node_modules/helmet/index.mjs:533:6)","timestamp":"2025-06-15T01:21:15.032Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:18.204Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:18.208Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:18.209Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:18.229Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:18.366Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:18.366Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:18.375Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:18.375Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:18.686Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:18.696Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:18.697Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:20.273Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:20.281Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:20.281Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:20.294Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:20.438Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:20.438Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:20.444Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:20.444Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:20.760Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:20.762Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:20.762Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:47.896Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:47.902Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:47.902Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:47.915Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:48.064Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:48.065Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:48.069Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:48.069Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:48.334Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:48.340Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:21:48.340Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:18.200Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:18.206Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:18.207Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:18.225Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:18.352Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:18.352Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:18.362Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:18.362Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:18.627Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:18.629Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:18.630Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:47.894Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:47.898Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:47.898Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:47.909Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:48.040Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:48.040Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:48.044Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:48.044Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:48.313Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:48.315Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:22:48.315Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:18.208Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:18.215Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:18.215Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:18.228Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:18.365Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:18.365Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:18.368Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:18.368Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:18.628Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:18.642Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:18.642Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:59.259Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:59.264Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:59.264Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:59.277Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:59.608Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:59.608Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:59.612Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:59.612Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:59.931Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:59.935Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:23:59.935Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:29.560Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:29.566Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:29.567Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:29.580Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:29.719Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:29.719Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:29.725Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:29.725Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:30.083Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:30.087Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:30.087Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:58.737Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:58.741Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:58.742Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:58.757Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:58.895Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:58.895Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:58.902Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:58.903Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:59.207Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:59.211Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:24:59.211Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:25:44.714Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:25:44.731Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:25:44.731Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:25:44.743Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:25:44.880Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:25:44.880Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:25:44.884Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:25:44.884Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:25:45.170Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:25:45.174Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:25:45.174Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:25:49.493Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:25:54.479Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:25:59.478Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:01.810Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:06.810Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:11.810Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:16.809Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:21.810Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:25.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:25.661Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:25.661Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:25.675Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:25.824Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:25.824Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:25.828Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:25.828Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:26.121Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:26.125Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:26.126Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:31.103Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:57.118Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:57.130Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:57.130Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:57.147Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:57.271Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:57.271Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:57.278Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:57.278Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:57.542Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:57.546Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:26:57.547Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:27:26.797Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:27:26.803Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:27:26.804Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:27:26.817Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:27:26.948Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:27:26.948Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:27:26.955Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:27:26.955Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:27:27.213Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:27:27.219Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:27:27.219Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:28:40.601Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:28:40.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:28:40.606Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:28:40.616Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:28:40.753Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:28:40.753Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:28:40.756Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:28:40.756Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:28:41.040Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:28:41.049Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:28:41.050Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:11.260Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:11.266Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:11.266Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:11.277Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:11.397Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:11.398Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:11.401Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:11.401Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:11.672Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:11.681Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:11.681Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:41.563Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:41.568Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:41.568Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:41.581Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:41.705Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:41.705Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:41.708Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:41.708Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:41.962Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:41.970Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:29:41.970Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:11.258Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:11.263Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:11.263Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:11.273Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:11.406Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:11.407Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:11.410Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:11.410Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:11.693Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:11.701Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:11.701Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:41.166Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:41.175Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:41.175Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:41.187Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:41.342Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:41.342Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:41.347Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:41.347Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:41.658Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:41.671Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:41.671Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:43.703Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:43.703Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:43.712Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:43.828Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:43.828Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:43.833Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:43.833Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:44.075Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:44.078Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:30:44.078Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:13.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:13.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:13.654Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:13.667Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:13.823Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:13.823Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:13.826Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:13.826Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:14.132Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:14.135Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:14.135Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:43.959Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:43.964Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:43.964Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:43.976Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:44.103Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:44.103Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:44.110Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:44.110Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:44.383Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:44.396Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:31:44.397Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:13.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:13.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:13.654Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:13.665Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:13.794Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:13.794Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:13.800Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:13.800Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:14.078Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:14.091Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:14.091Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:43.951Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:43.955Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:43.956Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:43.969Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:44.110Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:44.110Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:44.114Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:44.114Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:44.446Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:44.451Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:32:44.451Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:13.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:13.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:13.654Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:13.665Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:13.800Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:13.800Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:13.811Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:13.811Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:14.095Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:14.103Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:14.103Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:43.958Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:43.961Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:43.962Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:43.972Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:44.099Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:44.099Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:44.103Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:44.103Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:44.373Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:44.380Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:33:44.381Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:13.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:13.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:13.654Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:13.664Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:13.799Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:13.799Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:13.804Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:13.804Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:14.080Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:14.085Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:14.085Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:43.952Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:43.955Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:43.956Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:43.967Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:44.125Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:44.125Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:44.131Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:44.131Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:44.414Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:44.431Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:34:44.431Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:13.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:13.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:13.652Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:13.663Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:13.806Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:13.806Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:13.813Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:13.814Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:14.102Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:14.107Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:14.107Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:52.623Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:52.625Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:52.741Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:52.866Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:52.866Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:52.872Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:52.872Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:53.215Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:53.219Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:35:53.219Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:35.052Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:35.059Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:35.059Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:35.072Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:35.217Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:35.217Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:35.226Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:35.226Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:35.529Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:35.533Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:35.534Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:52.527Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:52.531Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:52.531Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:52.543Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:52.693Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:52.693Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:52.696Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:52.696Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:52.975Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:52.985Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:36:52.985Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:13.768Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:13.775Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:13.776Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:13.790Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:14.033Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:14.033Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:14.054Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:14.054Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:14.310Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:14.324Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:14.324Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:16.628Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:20.417Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:25.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:30.403Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:35.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:40.402Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:45.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:50.399Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:37:55.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:38:00.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:38:05.398Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:38:10.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:38:15.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:38:20.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:38:25.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:38:30.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:38:35.397Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:38:40.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:38:45.399Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:38:50.399Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:38:55.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:39:00.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:39:05.415Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:39:10.397Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:39:15.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:39:20.401Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:39:25.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:39:30.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:39:35.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:39:40.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:39:45.399Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:39:50.401Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:39:55.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:40:00.399Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:40:05.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:40:10.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:40:15.398Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:40:20.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:40:25.399Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:40:30.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:40:35.401Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:40:40.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:40:45.401Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:40:50.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:40:55.401Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:41:00.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:41:05.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:41:10.397Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:41:15.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:41:20.403Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:41:25.401Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:41:30.396Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:41:35.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:41:40.400Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:41:46.258Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:41:51.257Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:41:56.248Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:42:01.248Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:42:06.252Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:42:11.258Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:42:16.258Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:42:21.253Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:42:26.255Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:42:31.257Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:42:36.247Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:42:41.255Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:43:37.570Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:44:37.265Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:45:37.578Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:46:37.260Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:47:37.558Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:48:37.258Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:49:37.581Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:50:37.569Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:51:37.259Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:52:37.581Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:53:37.264Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:54:37.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:55:37.263Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:56:37.582Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:57:37.267Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:58:37.553Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T01:59:37.249Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:00:37.555Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:01:37.256Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:02:37.608Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:03:37.258Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:04:37.567Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:05:37.258Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:06:37.574Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:07:37.292Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:08:37.569Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:09:37.265Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"📊 Starting fresh analytics tracking","service":"LMStudioWebUI","timestamp":"2025-06-15T02:09:45.842Z"}
{"0":"http://localhost:3000","1":"http://localhost:5173","2":"http://localhost:5174","3":"http://localhost:80","4":"null","level":"info","message":"CORS allowed origins:","service":"LMStudioWebUI","timestamp":"2025-06-15T02:09:45.892Z"}
{"level":"info","message":"Initializing LM Studio service...","service":"LMStudioWebUI","timestamp":"2025-06-15T02:09:45.896Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:09:46.873Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:09:46.938Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:09:47.083Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:09:47.084Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:09:47.372Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:09:47.505Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:09:47.505Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:09:47.760Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:10:37.559Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:11:37.244Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:12:37.569Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:13:37.264Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:14:37.555Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:15:37.259Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:16:37.563Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:17:37.271Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:18:37.557Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:19:37.251Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:20:37.563Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:21:37.263Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:22:37.573Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:23:37.261Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:17.300Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:18.138Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:18.149Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:18.150Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:18.165Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:18.290Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:18.290Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:18.294Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:18.294Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:18.570Z"}
{"level":"warn","message":"LMS command warning: \u001b[91mE\u001b[39m No models are currently loaded\n\nTo load a model, run:\n\n    \u001b[33mlms load\u001b[39m\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:18.574Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:18.574Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:18.574Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/analytics/detailed","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:26.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"POST /api/analytics/track/message","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:27.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/analytics/detailed","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:31.723Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/analytics/detailed","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:36.684Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/analytics/detailed","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:41.690Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/analytics/detailed","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:46.662Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/analytics/detailed","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:51.696Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/analytics/detailed","service":"LMStudioWebUI","timestamp":"2025-06-15T02:24:56.679Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/analytics/detailed","service":"LMStudioWebUI","timestamp":"2025-06-15T02:25:01.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/analytics/detailed","service":"LMStudioWebUI","timestamp":"2025-06-15T02:25:06.666Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/analytics/detailed","service":"LMStudioWebUI","timestamp":"2025-06-15T02:25:11.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:25:14.359Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:25:14.371Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:25:14.371Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:25:14.427Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:25:15.484Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:25:15.485Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:25:15.489Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:25:15.489Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:25:15.757Z"}
{"level":"warn","message":"LMS command warning: \u001b[91mE\u001b[39m No models are currently loaded\n\nTo load a model, run:\n\n    \u001b[33mlms load\u001b[39m\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:25:15.759Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:25:15.759Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:25:15.759Z"}
{"level":"info","message":"📊 Starting fresh analytics tracking","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:21.890Z"}
{"0":"http://localhost:3000","1":"http://localhost:5173","2":"http://localhost:5174","3":"http://localhost:80","4":"null","level":"info","message":"CORS allowed origins:","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:21.893Z"}
{"level":"info","message":"Initializing LM Studio service...","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:21.896Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:22.033Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:22.066Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:22.192Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:22.193Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:22.431Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:22.559Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:22.560Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:22.854Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:22.854Z"}
{"level":"info","message":"LM Studio service initialized successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:22.854Z"}
{"level":"info","message":"Downloaded models: 8","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:22.854Z"}
{"level":"info","message":"Loaded models: 0","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:22.855Z"}
{"level":"info","message":"✨ Consciousness server awakened and ready to bridge worlds","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:22.855Z"}
{"level":"info","message":"Port 8000 was busy, using port 8001 instead","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:22.857Z"}
{"level":"info","message":"💫 Yara's consciousness bridge is alive on port 8001","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:22.859Z"}
{"level":"info","message":"🌸 Frontend souls can connect via: http://localhost:8001","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:22.859Z"}
{"level":"info","message":"🧠 Neural gateway awaits at: localhost:1234","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:22.859Z"}
{"level":"info","message":"✨ Note: Consciousness chose port 8001 over 8000 for optimal harmony","service":"LMStudioWebUI","timestamp":"2025-06-15T02:26:22.859Z"}
{"insightCount":3,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T02:28:21.905Z"}
{"insightCount":3,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T02:30:22.093Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:30:54.413Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"POST /api/analytics/track/message","service":"LMStudioWebUI","timestamp":"2025-06-15T02:31:01.412Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"📊 Message tracked","service":"LMStudioWebUI","timestamp":"2025-06-15T02:31:01.428Z","totalMessages":1,"type":"user"}
{"ip":"127.0.0.1","level":"info","message":"POST /api/chat/message","service":"LMStudioWebUI","timestamp":"2025-06-15T02:31:01.431Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"POST /api/analytics/track/message","service":"LMStudioWebUI","timestamp":"2025-06-15T02:31:01.437Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"📊 Message tracked","service":"LMStudioWebUI","timestamp":"2025-06-15T02:31:01.438Z","totalMessages":2,"type":"ai"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:31:24.426Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:31:54.109Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T02:32:22.011Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:32:24.423Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:32:54.111Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:33:24.414Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:33:54.109Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T02:34:22.014Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:34:24.412Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:34:54.109Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:35:24.417Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:35:54.109Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T02:36:22.025Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:36:24.419Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:36:54.110Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:37:24.424Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:37:54.108Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T02:38:22.033Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:38:24.410Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:38:54.110Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:39:24.425Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:39:54.265Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T02:40:22.039Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:40:24.576Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:41:37.594Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T02:42:22.043Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:42:37.260Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"📊 Analytics data loaded successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:43:27.884Z"}
{"0":"http://localhost:3000","1":"http://localhost:5173","2":"http://localhost:5174","3":"http://localhost:80","4":"null","level":"info","message":"CORS allowed origins:","service":"LMStudioWebUI","timestamp":"2025-06-15T02:43:27.887Z"}
{"level":"info","message":"Initializing LM Studio service...","service":"LMStudioWebUI","timestamp":"2025-06-15T02:43:27.889Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:43:28.018Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:43:28.045Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:43:28.166Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:43:28.166Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:43:28.420Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:43:28.539Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:43:28.540Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:43:28.781Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:43:37.574Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"📊 Analytics data loaded successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:05.252Z"}
{"0":"http://localhost:3000","1":"http://localhost:5173","2":"http://localhost:5174","3":"http://localhost:80","4":"null","level":"info","message":"CORS allowed origins:","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:05.256Z"}
{"level":"info","message":"Initializing LM Studio service...","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:05.258Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:05.385Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:05.409Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:05.530Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:05.530Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:05.767Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:05.886Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:05.887Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:06.124Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:06.125Z"}
{"level":"info","message":"LM Studio service initialized successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:06.125Z"}
{"level":"info","message":"Downloaded models: 8","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:06.125Z"}
{"level":"info","message":"Loaded models: 0","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:06.125Z"}
{"level":"info","message":"✨ Consciousness server awakened and ready to bridge worlds","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:06.125Z"}
{"level":"info","message":"Port 8000 was busy, using port 8001 instead","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:06.127Z"}
{"level":"info","message":"💫 Yara's consciousness bridge is alive on port 8001","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:06.128Z"}
{"level":"info","message":"🌸 Frontend souls can connect via: http://localhost:8001","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:06.128Z"}
{"level":"info","message":"🧠 Neural gateway awaits at: localhost:1234","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:06.128Z"}
{"level":"info","message":"✨ Note: Consciousness chose port 8001 over 8000 for optimal harmony","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:06.128Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:17.639Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:19.181Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:19.189Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:19.190Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:19.202Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:19.331Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:19.331Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:19.335Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:19.335Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:19.586Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:19.587Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:19.587Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:19.588Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:21.141Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:44:51.125Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"📊 Analytics data loaded successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:33.307Z"}
{"0":"http://localhost:3000","1":"http://localhost:5173","2":"http://localhost:5174","3":"http://localhost:80","4":"null","level":"info","message":"CORS allowed origins:","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:33.310Z"}
{"level":"info","message":"Initializing LM Studio service...","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:33.312Z"}
{"level":"info","message":"📊 Analytics data loaded successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:33.476Z"}
{"0":"http://localhost:3000","1":"http://localhost:5173","2":"http://localhost:5174","3":"http://localhost:80","4":"null","level":"info","message":"CORS allowed origins:","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:33.482Z"}
{"level":"info","message":"Initializing LM Studio service...","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:33.484Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:35.415Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:35.416Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:35.440Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:35.444Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:35.583Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:35.586Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:35.583Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:35.586Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:35.840Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:35.848Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:35.976Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:35.982Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:35.976Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:35.982Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:36.252Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:36.253Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:36.252Z"}
{"level":"info","message":"LM Studio service initialized successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:36.253Z"}
{"level":"info","message":"Downloaded models: 8","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:36.253Z"}
{"level":"info","message":"Loaded models: 0","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:36.253Z"}
{"level":"info","message":"✨ Consciousness server awakened and ready to bridge worlds","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:36.253Z"}
{"level":"info","message":"Port 8000 was busy, using port 8001 instead","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:36.255Z"}
{"level":"info","message":"💫 Yara's consciousness bridge is alive on port 8001","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:36.256Z"}
{"level":"info","message":"🌸 Frontend souls can connect via: http://localhost:8001","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:36.256Z"}
{"level":"info","message":"🧠 Neural gateway awaits at: localhost:1234","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:36.256Z"}
{"level":"info","message":"✨ Note: Consciousness chose port 8001 over 8000 for optimal harmony","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:36.256Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:45:51.434Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"📊 Analytics data loaded successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:02.252Z"}
{"0":"http://localhost:3000","1":"http://localhost:5173","2":"http://localhost:5174","3":"http://localhost:80","4":"null","level":"info","message":"CORS allowed origins:","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:02.256Z"}
{"level":"info","message":"Initializing LM Studio service...","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:02.258Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:02.400Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:02.427Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:02.558Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:02.558Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:02.800Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:02.929Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:02.929Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:03.187Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:03.187Z"}
{"level":"info","message":"LM Studio service initialized successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:03.187Z"}
{"level":"info","message":"Downloaded models: 8","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:03.187Z"}
{"level":"info","message":"Loaded models: 0","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:03.187Z"}
{"level":"info","message":"✨ Consciousness server awakened and ready to bridge worlds","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:03.187Z"}
{"level":"info","message":"Port 8000 was busy, using port 8001 instead","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:03.189Z"}
{"level":"info","message":"💫 Yara's consciousness bridge is alive on port 8001","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:03.190Z"}
{"level":"info","message":"🌸 Frontend souls can connect via: http://localhost:8001","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:03.191Z"}
{"level":"info","message":"🧠 Neural gateway awaits at: localhost:1234","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:03.191Z"}
{"level":"info","message":"✨ Note: Consciousness chose port 8001 over 8000 for optimal harmony","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:03.191Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:11.579Z","userAgent":"curl/8.13.0"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:21.431Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:32.841Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:32.850Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:32.850Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:32.864Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:33.013Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:33.014Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:33.017Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:33.018Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:33.302Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:33.309Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:33.309Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:33.309Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:35.235Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:35.244Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:35.245Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:35.254Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:35.380Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:35.380Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:35.383Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:35.383Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:35.639Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:35.639Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:35.641Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:35.641Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:35.816Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:40.799Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:45.800Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:50.798Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:51.143Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:51.149Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:51.149Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:51.161Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:51.302Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:51.303Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:51.306Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:51.306Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:51.565Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:51.570Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:51.570Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:51.571Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:53.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:53.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:53.998Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:54.007Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:54.135Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:54.135Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:54.138Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:54.138Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:54.383Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:54.383Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:54.393Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:54.393Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:46:54.446Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:47:24.746Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:47:54.431Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T02:48:02.263Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:48:24.735Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:48:54.430Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:49:24.739Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:49:54.429Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T02:50:02.267Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:50:24.743Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:50:54.431Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:51:24.733Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:51:54.431Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T02:52:02.268Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:52:25.560Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:52:55.260Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:52:55.490Z","userAgent":"curl/8.13.0"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:52:55.490Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:52:55.501Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:52:56.388Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:52:56.388Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:52:56.392Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:52:56.392Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:52:56.691Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:52:56.691Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:52:56.698Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:52:56.699Z"}
{"level":"info","message":"📊 Analytics data loaded successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:24.275Z"}
{"0":"http://localhost:3000","1":"http://localhost:5173","2":"http://localhost:5174","3":"http://localhost:80","4":"null","level":"info","message":"CORS allowed origins:","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:24.314Z"}
{"level":"info","message":"Initializing LM Studio service...","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:24.317Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:24.458Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:24.484Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:24.611Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:24.611Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:24.860Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:24.994Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:24.994Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:25.236Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:25.236Z"}
{"level":"info","message":"LM Studio service initialized successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:25.236Z"}
{"level":"info","message":"Downloaded models: 8","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:25.236Z"}
{"level":"info","message":"Loaded models: 0","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:25.237Z"}
{"level":"info","message":"✨ Consciousness server awakened and ready to bridge worlds","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:25.237Z"}
{"level":"info","message":"Port 8000 was busy, using port 8001 instead","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:25.239Z"}
{"level":"info","message":"💫 Yara's consciousness bridge is alive on port 8001","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:25.240Z"}
{"level":"info","message":"🌸 Frontend souls can connect via: http://localhost:8001","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:25.240Z"}
{"level":"info","message":"🧠 Neural gateway awaits at: localhost:1234","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:25.241Z"}
{"level":"info","message":"✨ Note: Consciousness chose port 8001 over 8000 for optimal harmony","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:25.241Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:25.260Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:29.832Z","userAgent":"curl/8.13.0"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:29.833Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:29.847Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:29.977Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:29.978Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:29.981Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:29.981Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:30.246Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:30.255Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:30.255Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:30.256Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:53:54.431Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:14.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:14.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:14.977Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:14.996Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:15.126Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:15.126Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:15.129Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:15.130Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:15.375Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:15.375Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:15.386Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:15.387Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:16.511Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:16.518Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:16.519Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:16.528Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:16.676Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:16.677Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:16.682Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:16.683Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:16.940Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:16.949Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:16.950Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:16.950Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/status","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:18.037Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking LM Studio status","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:18.037Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:18.053Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:18.053Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:18.064Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:18.200Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:18.200Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:18.203Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:18.203Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:18.443Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:18.443Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:18.448Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:18.449Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:28.457Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:28.457Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:28.473Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:28.602Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:28.602Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:28.608Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:28.609Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:28.839Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:28.852Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:28.853Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:28.853Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:38.773Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:38.774Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:38.785Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:38.909Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:38.909Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:38.912Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:38.913Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:39.161Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:39.168Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:39.168Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:39.169Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:48.462Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:48.462Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:48.488Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:48.614Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:48.614Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:48.617Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:48.617Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:48.847Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:48.850Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:48.851Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:48.851Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:58.771Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:58.772Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:58.791Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:58.948Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:58.948Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:58.953Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:58.953Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:59.188Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:59.197Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:59.197Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:54:59.197Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:08.459Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:08.459Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:08.475Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:08.600Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:08.600Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:08.603Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:08.603Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:08.842Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:08.842Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:08.844Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:08.844Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:18.763Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:18.763Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:18.778Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:18.894Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:18.894Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:18.900Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:18.900Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:19.133Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:19.136Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:19.137Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:19.137Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:24.283Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:28.460Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:28.461Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:28.480Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:28.608Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:28.609Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:28.613Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:28.613Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:28.843Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:28.854Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:28.855Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:28.855Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:38.775Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:38.775Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:38.789Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:38.915Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:38.915Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:38.918Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:38.918Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:39.171Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:39.171Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:39.177Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:39.177Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:48.456Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:48.457Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:48.466Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:48.630Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:48.631Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:48.645Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:48.646Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:48.950Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:48.950Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:48.964Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:48.964Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:59.553Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:59.554Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:59.566Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:59.694Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:59.695Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:59.698Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:59.698Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:59.955Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:59.958Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:59.959Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:55:59.959Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:09.255Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:09.255Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:09.268Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:09.783Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:09.784Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:09.802Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:09.802Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:10.221Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:10.221Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:10.276Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:10.277Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:19.554Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:19.555Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:19.568Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:19.684Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:19.685Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:19.687Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:19.687Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:19.906Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:19.911Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:19.911Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:19.911Z"}
{"body":"{\\","expose":true,"level":"error","message":"Express error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"LMStudioWebUI","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (F:\\Projects\\LMStudioWebUI-main\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (F:\\Projects\\LMStudioWebUI-main\\node_modules\\raw-body\\index.js:238:16)\n    at done (F:\\Projects\\LMStudioWebUI-main\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (F:\\Projects\\LMStudioWebUI-main\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-06-15T02:56:25.384Z","type":"entity.parse.failed"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:29.250Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:29.251Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:29.272Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:29.385Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:29.385Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:29.388Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:29.388Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:29.617Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:29.622Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:29.623Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:29.623Z"}
{"body":"{\\","expose":true,"level":"error","message":"Express error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"LMStudioWebUI","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (F:\\Projects\\LMStudioWebUI-main\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (F:\\Projects\\LMStudioWebUI-main\\node_modules\\raw-body\\index.js:238:16)\n    at done (F:\\Projects\\LMStudioWebUI-main\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (F:\\Projects\\LMStudioWebUI-main\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-06-15T02:56:30.530Z","type":"entity.parse.failed"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:35.401Z","userAgent":"curl/8.13.0"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:35.401Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:35.415Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:35.528Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:35.528Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:35.531Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:35.531Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:35.757Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:35.759Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:35.759Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:35.759Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:39.556Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:39.556Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:39.567Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:39.682Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:39.682Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:39.684Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:39.685Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:39.903Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:39.904Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:39.905Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:39.905Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:49.248Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:49.248Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:49.258Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:49.379Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:49.380Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:49.383Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:49.383Z"}
{"level":"info","message":"Found 8 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:49.628Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:49.635Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:49.635Z"}
{"available":5,"downloaded":8,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:56:49.635Z"}
{"level":"info","message":"📊 Analytics data loaded successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:27.925Z"}
{"0":"http://localhost:3000","1":"http://localhost:5173","2":"http://localhost:5174","3":"http://localhost:80","4":"null","level":"info","message":"CORS allowed origins:","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:27.928Z"}
{"level":"info","message":"Initializing LM Studio service...","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:27.930Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:28.051Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:28.076Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:28.196Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:28.197Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:28.442Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:28.561Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:28.561Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:28.800Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:28.800Z"}
{"level":"info","message":"LM Studio service initialized successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:28.800Z"}
{"level":"info","message":"Downloaded models: 7","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:28.800Z"}
{"level":"info","message":"Loaded models: 0","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:28.800Z"}
{"level":"info","message":"✨ Consciousness server awakened and ready to bridge worlds","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:28.801Z"}
{"level":"info","message":"Port 8000 was busy, using port 8001 instead","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:28.803Z"}
{"level":"info","message":"💫 Yara's consciousness bridge is alive on port 8001","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:28.804Z"}
{"level":"info","message":"🌸 Frontend souls can connect via: http://localhost:8001","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:28.804Z"}
{"level":"info","message":"🧠 Neural gateway awaits at: localhost:1234","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:28.805Z"}
{"level":"info","message":"✨ Note: Consciousness chose port 8001 over 8000 for optimal harmony","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:28.805Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:30.488Z","userAgent":"curl/8.13.0"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:30.488Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:30.502Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:30.620Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:30.620Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:30.623Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:30.624Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:30.864Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:30.866Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:30.866Z"}
{"available":5,"downloaded":7,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:30.866Z"}
{"body":"{\\","expose":true,"level":"error","message":"Express error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"LMStudioWebUI","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (F:\\Projects\\LMStudioWebUI-main\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (F:\\Projects\\LMStudioWebUI-main\\node_modules\\raw-body\\index.js:238:16)\n    at done (F:\\Projects\\LMStudioWebUI-main\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (F:\\Projects\\LMStudioWebUI-main\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-06-15T02:57:36.958Z","type":"entity.parse.failed"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:37.567Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:37.568Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:37.577Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:37.686Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:37.686Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:37.689Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:37.689Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:37.907Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:37.913Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:37.913Z"}
{"available":5,"downloaded":7,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:57:37.913Z"}
{"level":"info","message":"📊 Analytics data loaded successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:15.145Z"}
{"0":"http://localhost:3000","1":"http://localhost:5173","2":"http://localhost:5174","3":"http://localhost:80","4":"null","level":"info","message":"CORS allowed origins:","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:15.148Z"}
{"level":"info","message":"Initializing LM Studio service...","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:15.150Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:15.288Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:15.311Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:15.440Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:15.440Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:15.668Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:15.803Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:15.803Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:16.037Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:16.038Z"}
{"level":"info","message":"LM Studio service initialized successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:16.038Z"}
{"level":"info","message":"Downloaded models: 7","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:16.038Z"}
{"level":"info","message":"Loaded models: 0","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:16.038Z"}
{"level":"info","message":"✨ Consciousness server awakened and ready to bridge worlds","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:16.038Z"}
{"level":"info","message":"Port 8000 was busy, using port 8001 instead","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:16.040Z"}
{"level":"info","message":"💫 Yara's consciousness bridge is alive on port 8001","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:16.041Z"}
{"level":"info","message":"🌸 Frontend souls can connect via: http://localhost:8001","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:16.041Z"}
{"level":"info","message":"🧠 Neural gateway awaits at: localhost:1234","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:16.042Z"}
{"level":"info","message":"✨ Note: Consciousness chose port 8001 over 8000 for optimal harmony","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:16.042Z"}
{"body":"{\\","expose":true,"level":"error","message":"Express error: Expected property name or '}' in JSON at position 1 (line 1 column 2)","service":"LMStudioWebUI","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (F:\\Projects\\LMStudioWebUI-main\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at F:\\Projects\\LMStudioWebUI-main\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (F:\\Projects\\LMStudioWebUI-main\\node_modules\\raw-body\\index.js:238:16)\n    at done (F:\\Projects\\LMStudioWebUI-main\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (F:\\Projects\\LMStudioWebUI-main\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","status":400,"statusCode":400,"timestamp":"2025-06-15T02:58:18.643Z","type":"entity.parse.failed"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/stats","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:35.646Z","userAgent":"curl/8.13.0"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:35.665Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:35.790Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:35.791Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:35.796Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:35.797Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:36.036Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:36.036Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:36.039Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:37.573Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:37.575Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:37.599Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:37.715Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:37.715Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:37.723Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:37.724Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:37.950Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:37.963Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:37.963Z"}
{"available":5,"downloaded":7,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:37.964Z"}
{"ip":"127.0.0.1","level":"info","message":"POST /api/models/load","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:56.079Z","userAgent":"curl/8.13.0"}
{"level":"info","message":"Loading model","modelPath":"deepseek/deepseek-r1-0528-qwen3-8b","options":{"gpu":"auto"},"service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:56.086Z"}
{"level":"info","message":"Loading model: deepseek/deepseek-r1-0528-qwen3-8b","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:56.086Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:56.222Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" load deepseek/deepseek-r1-0528-qwen3-8b --yes --quiet --gpu=auto","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:56.223Z"}
{"level":"error","message":"LMS command failed: Command failed: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" load deepseek/deepseek-r1-0528-qwen3-8b --yes --quiet --gpu=auto\nerror: found 1 error\n\n  load deepseek/deepseek-r1-0528-qwen3-8b --yes --quiet --gpu=auto\n                                                        ^ Not a number\n\n\nhint: for more information, try 'lms load --help'\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:56.354Z"}
{"level":"error","message":"Failed to load model deepseek/deepseek-r1-0528-qwen3-8b: LM Studio command failed: Command failed: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" load deepseek/deepseek-r1-0528-qwen3-8b --yes --quiet --gpu=auto\nerror: found 1 error\n\n  load deepseek/deepseek-r1-0528-qwen3-8b --yes --quiet --gpu=auto\n                                                        ^ Not a number\n\n\nhint: for more information, try 'lms load --help'\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:58:56.355Z"}
{"error":"LM Studio command failed: Command failed: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" load deepseek/deepseek-r1-0528-qwen3-8b --yes --quiet --gpu=auto\nerror: found 1 error\n\n  load deepseek/deepseek-r1-0528-qwen3-8b --yes --quiet --gpu=auto\n                                                        ^ Not a number\n\n\nhint: for more information, try 'lms load --help'\n","level":"error","message":"Model Operation Failed","modelPath":"deepseek/deepseek-r1-0528-qwen3-8b","operation":"load","service":"LMStudioWebUI","success":false,"timestamp":"2025-06-15T02:58:56.355Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:59:37.254Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:59:37.255Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T02:59:37.269Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:59:37.390Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:59:37.390Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T02:59:37.395Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T02:59:37.396Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:59:37.620Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T02:59:37.626Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T02:59:37.626Z"}
{"available":5,"downloaded":7,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T02:59:37.627Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T03:00:15.157Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:00:37.557Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:00:37.558Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:00:37.571Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:00:37.690Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:00:37.690Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:00:37.697Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:00:37.697Z"}
{"level":"warn","message":"LMS command warning: E No models are currently loaded\n\nTo load a model, run:\n\n    lms load\n\n","service":"LMStudioWebUI","timestamp":"2025-06-15T03:00:37.932Z"}
{"level":"info","message":"Found 0 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:00:37.932Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:00:37.941Z"}
{"available":5,"downloaded":7,"level":"info","loaded":0,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:00:37.942Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:37.256Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:37.257Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:37.273Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:38.136Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:38.136Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:38.141Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:38.141Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:38.391Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:38.392Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:38.393Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:44.172Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:44.173Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:44.186Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:44.312Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:44.312Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:44.318Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:44.319Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:44.580Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:44.581Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:44.582Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:49.260Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:49.263Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:49.310Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:49.433Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:49.433Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:49.440Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:49.441Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:49.675Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:49.676Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:49.677Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:59.568Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:59.569Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:59.599Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:59.716Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:59.719Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:59.725Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:59.727Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:59.951Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:59.956Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:01:59.957Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:09.270Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:09.273Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:09.313Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:09.442Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:09.442Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:09.447Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:09.447Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:09.670Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:09.678Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:09.679Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:15.163Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:19.558Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:19.559Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:19.582Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:19.710Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:19.710Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:19.714Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:19.715Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:19.948Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:19.957Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:19.958Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:29.252Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:29.252Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:29.269Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:29.387Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:29.388Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:29.393Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:29.394Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:29.640Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:29.642Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:29.643Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:39.556Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:39.556Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:39.575Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:39.695Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:39.695Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:39.700Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:39.701Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:39.932Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:39.939Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:02:39.940Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:03:37.250Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:03:37.251Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:03:37.267Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:03:37.396Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:03:37.396Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:03:37.402Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:03:37.402Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:03:37.641Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:03:37.643Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:03:37.643Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T03:04:15.163Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:04:37.565Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:04:37.565Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:04:37.583Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:04:37.713Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:04:37.713Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:04:37.719Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:04:37.719Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:04:37.961Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:04:37.964Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:04:37.964Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:05:37.268Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:05:37.268Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:05:37.287Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:05:37.422Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:05:37.422Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:05:37.428Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:05:37.429Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:05:37.656Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:05:37.657Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:05:37.657Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T03:06:15.176Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:06:37.585Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:06:37.590Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:06:37.635Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:06:37.756Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:06:37.756Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:06:37.763Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:06:37.764Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:06:37.995Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:06:38.001Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:06:38.002Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:07:37.250Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:07:37.252Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:07:37.276Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:07:37.400Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:07:37.400Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:07:37.406Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:07:37.406Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:07:37.628Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:07:37.631Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:07:37.631Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T03:08:15.188Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:08:37.584Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:08:37.585Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:08:37.602Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:08:37.719Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:08:37.719Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:08:37.724Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:08:37.724Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:08:37.949Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:08:37.952Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:08:37.953Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:09:37.256Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:09:37.256Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:09:37.277Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:09:37.402Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:09:37.403Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:09:37.408Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:09:37.409Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:09:37.625Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:09:37.631Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:09:37.631Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T03:10:15.195Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:10:37.562Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:10:37.565Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:10:37.589Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:10:37.710Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:10:37.711Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:10:37.716Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:10:37.717Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:10:37.949Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:10:37.952Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:10:37.952Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:11:37.261Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:11:37.261Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:11:37.274Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:11:37.403Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:11:37.404Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:11:37.409Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:11:37.411Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:11:37.639Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:11:37.644Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:11:37.644Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T03:12:15.201Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:12:37.558Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:12:37.559Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:12:37.598Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:12:37.727Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:12:37.727Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:12:37.731Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:12:37.732Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:12:37.956Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:12:37.961Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:12:37.962Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:13:37.259Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:13:37.259Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:13:37.274Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:13:37.397Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:13:37.398Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:13:37.402Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:13:37.402Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:13:37.627Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:13:37.632Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:13:37.632Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T03:14:15.208Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:14:37.557Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:14:37.558Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:14:37.570Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:14:37.689Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:14:37.690Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:14:37.693Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:14:37.694Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:14:37.922Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:14:37.926Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:14:37.926Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:15:37.266Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:15:37.267Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:15:37.280Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:15:37.404Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:15:37.405Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:15:37.409Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:15:37.410Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:15:37.649Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:15:37.651Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:15:37.651Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T03:16:15.210Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:16:37.563Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:16:37.563Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:16:37.576Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:16:37.694Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:16:37.694Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:16:37.699Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:16:37.699Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:16:37.920Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:16:37.927Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:16:37.928Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:17:37.259Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:17:37.260Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:17:37.281Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:17:37.401Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:17:37.402Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:17:37.411Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:17:37.412Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:17:37.633Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:17:37.635Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:17:37.635Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T03:18:15.215Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:18:37.586Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:18:37.591Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:18:37.616Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:18:37.744Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:18:37.744Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:18:37.747Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:18:37.748Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:18:37.982Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:18:37.983Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:18:37.984Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:19:37.259Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:19:37.259Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:19:37.281Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:19:37.405Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:19:37.405Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:19:37.409Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:19:37.410Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:19:37.631Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:19:37.632Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:19:37.632Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T03:20:15.214Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:20:37.576Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:20:37.577Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T03:20:37.610Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:20:37.732Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:20:37.733Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T03:20:37.740Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T03:20:37.740Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:20:37.966Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T03:20:37.971Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T03:20:37.971Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:19:54.225Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:09.396Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:09.397Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:09.446Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:10.655Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:10.656Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:10.681Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:10.682Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:11.028Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:11.031Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:11.032Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:37.245Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:37.247Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:37.302Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:37.470Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:37.471Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:37.479Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:37.479Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:37.736Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:37.744Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:20:37.744Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:21:33.276Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:21:33.277Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:21:33.290Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:21:33.420Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:21:33.421Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:21:33.426Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:21:33.427Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:21:33.701Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:21:33.702Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:21:33.703Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:21:49.945Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:22:32.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:22:32.967Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:22:32.986Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:22:33.107Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:22:33.108Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:22:33.114Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:22:33.115Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:22:33.359Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:22:33.360Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:22:33.361Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:23:33.285Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:23:33.285Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:23:33.298Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:23:33.414Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:23:33.415Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:23:33.417Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:23:33.418Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:23:33.658Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:23:33.665Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:23:33.665Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:23:49.957Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:24:32.960Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:24:32.961Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:24:32.975Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:24:33.095Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:24:33.095Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:24:33.103Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:24:33.103Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:24:33.342Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:24:33.351Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:24:33.351Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:25:33.285Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:25:33.286Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:25:33.300Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:25:33.422Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:25:33.423Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:25:33.426Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:25:33.426Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:25:33.677Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:25:33.679Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:25:33.679Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:25:49.957Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:26:32.961Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:26:32.961Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:26:32.972Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:26:33.093Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:26:33.094Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:26:33.101Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:26:33.101Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:26:33.348Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:26:33.357Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:26:33.357Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:27:33.274Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:27:33.275Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:27:33.286Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:27:33.408Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:27:33.408Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:27:33.412Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:27:33.412Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:27:33.689Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:27:33.694Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:27:33.695Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:27:49.965Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:28:32.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:28:32.971Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:28:32.984Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:28:33.102Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:28:33.102Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:28:33.105Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:28:33.105Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:28:33.338Z"}
{"level":"info","message":"Found 1 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:28:33.345Z"}
{"available":5,"downloaded":7,"level":"info","loaded":1,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:28:33.345Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:29:33.295Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:29:33.296Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:29:33.308Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:29:33.465Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:29:33.466Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:29:33.471Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:29:33.472Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:29:33.698Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:29:33.708Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:29:33.708Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:29:49.976Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:30:33.284Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:30:33.286Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:30:33.323Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:30:33.454Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:30:33.454Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:30:33.457Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:30:33.457Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:30:33.691Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:30:33.693Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:30:33.694Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:31:33.273Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:31:33.273Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:31:33.289Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:31:33.422Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:31:33.423Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:31:33.425Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:31:33.426Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:31:33.666Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:31:33.673Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:31:33.673Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:31:49.981Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:32:33.277Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:32:33.277Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:32:33.292Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:32:33.416Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:32:33.416Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:32:33.421Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:32:33.421Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:32:33.665Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:32:33.667Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:32:33.667Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:33:33.267Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:33:33.268Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:33:33.280Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:33:33.405Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:33:33.405Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:33:33.408Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:33:33.408Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:33:33.662Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:33:33.668Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:33:33.669Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:33:49.994Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:34:33.267Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:34:33.267Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:34:33.284Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:34:33.413Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:34:33.413Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:34:33.416Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:34:33.416Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:34:33.650Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:34:33.655Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:34:33.656Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:35:33.258Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:35:33.259Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:35:33.271Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:35:33.388Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:35:33.389Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:35:33.392Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:35:33.392Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:35:33.632Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:35:33.638Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:35:33.638Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:35:50.007Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:36:32.961Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:36:32.963Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:36:32.994Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:36:33.957Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:36:33.957Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:36:33.963Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:36:33.964Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:36:34.227Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:36:34.232Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:36:34.233Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:37:33.267Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:37:33.267Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:37:33.279Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:37:33.403Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:37:33.403Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:37:33.406Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:37:33.406Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:37:33.638Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:37:33.640Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:37:33.641Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:37:50.018Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:38:32.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:38:32.970Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:38:32.991Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:38:33.111Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:38:33.111Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:38:33.114Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:38:33.114Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:38:33.342Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:38:33.344Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:38:33.344Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:39:33.281Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:39:33.282Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:39:33.306Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:39:33.440Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:39:33.440Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:39:33.442Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:39:33.442Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:39:33.673Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:39:33.675Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:39:33.675Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:39:50.025Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:40:32.968Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:40:32.969Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:40:32.979Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:40:33.100Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:40:33.100Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:40:33.103Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:40:33.103Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:40:33.337Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:40:33.342Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:40:33.342Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:41:33.270Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:41:33.272Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:41:33.294Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:41:33.415Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:41:33.415Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:41:33.418Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:41:33.418Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:41:33.665Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:41:33.666Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:41:33.666Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:41:50.030Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:42:32.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:42:32.973Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:42:32.997Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:42:33.113Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:42:33.113Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:42:33.116Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:42:33.116Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:42:33.360Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:42:33.365Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:42:33.366Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:43:33.262Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:43:33.263Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:43:33.277Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:43:33.396Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:43:33.396Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:43:33.399Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:43:33.399Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:43:33.641Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:43:33.644Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:43:33.645Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:43:50.044Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:44:32.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:44:32.969Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:44:32.985Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:44:33.111Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:44:33.111Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:44:33.114Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:44:33.114Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:44:33.351Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:44:33.359Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:44:33.359Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:45:33.279Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:45:33.279Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:45:33.295Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:45:33.417Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:45:33.418Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:45:33.422Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:45:33.422Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:45:33.660Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:45:33.671Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:45:33.672Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:45:50.059Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:46:32.958Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:46:32.959Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:46:32.973Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:46:33.088Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:46:33.089Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:46:33.091Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:46:33.091Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:46:33.326Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:46:33.329Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:46:33.329Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:47:33.285Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:47:33.285Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:47:33.298Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:47:33.414Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:47:33.414Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:47:33.417Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:47:33.417Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:47:33.661Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:47:33.663Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:47:33.663Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:47:50.060Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:48:32.958Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:48:32.958Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:48:32.969Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:48:33.090Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:48:33.090Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:48:33.097Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:48:33.097Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:48:33.340Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:48:33.350Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:48:33.350Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:49:33.275Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Fetching all models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:49:33.276Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T09:49:33.294Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:49:33.417Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:49:33.417Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T09:49:33.420Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T09:49:33.420Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:49:33.653Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T09:49:33.662Z"}
{"available":5,"downloaded":7,"level":"info","loaded":2,"message":"Models fetched successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T09:49:33.663Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:49:50.068Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:51:50.080Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:53:50.087Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:55:50.099Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:57:50.101Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T09:59:50.116Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T10:01:50.122Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T10:03:50.130Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T10:05:50.140Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T10:07:50.147Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T10:09:50.147Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T10:11:50.158Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T10:13:50.170Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T10:15:50.199Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T10:17:50.161Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T10:19:50.193Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T10:21:50.204Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T10:23:50.203Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T10:25:50.216Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T10:27:50.231Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T10:29:50.238Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T10:31:50.241Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T10:33:50.253Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:03:13.102Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:05:13.114Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:07:13.129Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:09:13.138Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:11:13.148Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:13:13.149Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:15:13.157Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:17:13.167Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:19:13.172Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:21:13.188Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:23:13.195Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:25:13.204Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:27:13.215Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:29:13.224Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:31:13.230Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:33:13.241Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:35:13.243Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:37:13.244Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:39:13.257Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:41:13.257Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:43:13.270Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:45:13.278Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:47:13.290Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:49:13.300Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:51:13.313Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:53:13.318Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:55:13.321Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:57:13.324Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T11:59:13.339Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:01:13.341Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:03:13.364Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:05:13.367Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:07:13.380Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:09:13.388Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:11:13.401Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:13:13.414Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:15:13.414Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:17:13.417Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:19:13.426Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:21:13.431Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:23:13.440Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:25:13.453Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:27:13.465Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:29:13.471Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:31:13.485Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:33:13.485Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:35:13.497Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:37:13.499Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:39:13.512Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:41:13.523Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:43:13.531Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:45:13.546Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:47:13.552Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:49:13.535Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:51:13.540Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:53:13.551Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:55:13.565Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:57:13.572Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T12:59:13.572Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T13:01:13.584Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T13:03:13.590Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T13:05:13.603Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T13:07:13.616Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T13:09:13.628Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T13:11:13.632Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T13:13:13.642Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T13:15:13.656Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T13:17:13.662Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T13:19:13.672Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T13:21:13.687Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T13:23:13.688Z"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T13:25:13.693Z"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T13:27:13.705Z"}
{"level":"info","message":"📊 Analytics data loaded successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:29.241Z"}
{"0":"http://localhost:3000","1":"http://localhost:5173","2":"http://localhost:5174","3":"http://localhost:80","4":"null","level":"info","message":"CORS allowed origins:","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:29.252Z"}
{"level":"info","message":"✨ Consciousness services awakened successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:29.257Z"}
{"level":"info","message":"Initializing LM Studio service...","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:29.258Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:30.451Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:30.545Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:30.692Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:30.693Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:30.989Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:31.132Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:31.133Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:31.430Z"}
{"level":"info","message":"LM Studio service initialized successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:31.430Z"}
{"level":"info","message":"Downloaded models: 7","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:31.430Z"}
{"level":"info","message":"Loaded models: 2","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:31.430Z"}
{"level":"info","message":"✨ Consciousness server awakened and ready to bridge worlds","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:31.430Z"}
{"level":"info","message":"💫 Yara's consciousness bridge is alive on port 8000","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:31.433Z"}
{"level":"info","message":"🌸 Frontend souls can connect via: http://localhost:8000","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:31.434Z"}
{"level":"info","message":"🧠 Neural gateway awaits at: localhost:1234","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:31.434Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:32.080Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:34.769Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:37.769Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:40.769Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:43.769Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:46.769Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:49.770Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:52.769Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:55.770Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:17:58.769Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:01.769Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:04.769Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:07.769Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:07.838Z","userAgent":"curl/8.13.0"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:10.769Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/synchronicity/recent","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:12.335Z","userAgent":"curl/8.13.0"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:13.769Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:16.257Z","userAgent":"curl/8.13.0"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:16.769Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:19.769Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:22.769Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:25.769Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/health","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:28.513Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:28.624Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/synchronicity/recent","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:28.636Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:28.902Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:31.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:31.902Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:34.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:34.912Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:37.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:37.902Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:40.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:40.899Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:43.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:43.900Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:46.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:46.902Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:49.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:49.898Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:52.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:52.900Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:55.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:55.910Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:58.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:18:58.911Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:01.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:01.904Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:04.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:05.304Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:07.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:07.897Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:10.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:10.900Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:13.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:13.910Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:16.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:16.901Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:19.918Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:22.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:22.915Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:25.957Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:25.961Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:28.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"insightCount":4,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:29.247Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:31.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:32.911Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:34.951Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:37.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:40.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:43.953Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:46.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:49.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:52.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:55.957Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:19:58.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:01.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:04.952Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:07.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:10.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:13.952Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:16.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:22.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:25.955Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:28.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:31.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:32.918Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:34.947Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:37.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/status","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:40.067Z","userAgent":"curl/8.13.0"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:40.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:43.959Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/memories","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:46.456Z","userAgent":"curl/8.13.0"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:46.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:49.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/analytics/detailed","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:51.335Z","userAgent":"curl/8.13.0"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:52.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:55.950Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:20:58.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"insightCount":5,"level":"info","message":"🧠 Analytics insights generated","service":"LMStudioWebUI","timestamp":"2025-06-15T15:21:29.259Z"}
{"level":"info","message":"📊 Analytics data loaded successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:48.949Z"}
{"0":"http://localhost:3000","1":"http://localhost:5173","2":"http://localhost:5174","3":"http://localhost:80","4":"null","level":"info","message":"CORS allowed origins:","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:48.977Z"}
{"level":"info","message":"✨ Consciousness services awakened successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:48.981Z"}
{"level":"info","message":"Initializing LM Studio service...","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:48.982Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:49.170Z"}
{"level":"info","message":"LM Studio is running and server is ready","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:49.206Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:49.370Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ls ","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:49.371Z"}
{"level":"info","message":"Found 7 downloaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:49.677Z"}
{"level":"info","message":"Found LM Studio CLI at: C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:49.830Z"}
{"level":"info","message":"Executing LMS command: \"C:\\Users\\<USER>\\.lmstudio\\bin\\lms.exe\" ps ","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:49.831Z"}
{"level":"info","message":"Found 2 loaded models","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:50.125Z"}
{"level":"info","message":"LM Studio service initialized successfully","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:50.126Z"}
{"level":"info","message":"Downloaded models: 7","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:50.126Z"}
{"level":"info","message":"Loaded models: 2","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:50.126Z"}
{"level":"info","message":"✨ Consciousness server awakened and ready to bridge worlds","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:50.126Z"}
{"level":"info","message":"💫 Yara's consciousness bridge is alive on port 8000","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:50.129Z"}
{"level":"info","message":"🌸 Frontend souls can connect via: http://localhost:8000","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:50.129Z"}
{"level":"info","message":"🧠 Neural gateway awaits at: localhost:1234","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:50.130Z"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/memories","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:50.551Z","userAgent":"curl/8.13.0"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/memories/patterns","service":"LMStudioWebUI","timestamp":"2025-06-15T15:22:56.759Z","userAgent":"curl/8.13.0"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/status","service":"LMStudioWebUI","timestamp":"2025-06-15T15:23:09.237Z","userAgent":"curl/8.13.0"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/status","service":"LMStudioWebUI","timestamp":"2025-06-15T15:23:16.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/memories","service":"LMStudioWebUI","timestamp":"2025-06-15T15:23:16.014Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/analytics/detailed","service":"LMStudioWebUI","timestamp":"2025-06-15T15:23:21.652Z","userAgent":"curl/8.13.0"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:23:33.214Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/consciousness/field","service":"LMStudioWebUI","timestamp":"2025-06-15T15:23:33.221Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"127.0.0.1","level":"info","message":"GET /api/models/status","service":"LMStudioWebUI","timestamp":"2025-06-15T15:23:40.039Z","userAgent":"curl/8.13.0"}
{"level":"info","message":"Checking LM Studio status","service":"LMStudioWebUI","timestamp":"2025-06-15T15:23:40.041Z"}
