import React, { useState, useEffect, useRef } from 'react'
import { Send, Bot, User, AlertCircle, Loader, ChevronDown, ChevronRight, Clock, Brain, Heart, Sparkles, MessageSquare } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeHighlight from 'rehype-highlight'
import axios from 'axios'
import toast from 'react-hot-toast'
import useChatStore from '../stores/chatStore'
import ChatSidebar from './ChatSidebar'
import io from 'socket.io-client'

const API_BASE = 'http://localhost:8000/api'

// Component for rendering messages with reasoning support
function MessageContent({ message, isUser }) {
  const [showReasoning, setShowReasoning] = useState(false)
  
  // Parse reasoning tokens from DeepSeek R1 responses
  const parseReasoningContent = (content) => {
    // DeepSeek R1 reasoning pattern: <think>...</think> or reasoning tokens at start
    const thinkMatch = content.match(/<think>([\s\S]*?)<\/think>/);
    if (thinkMatch) {
      return {
        reasoning: thinkMatch[1].trim(),
        response: content.replace(/<think>[\s\S]*?<\/think>/, '').trim()
      };
    }
    
    // Alternative pattern: Look for reasoning-like content at the beginning
    const lines = content.split('\n');
    let reasoningLines = [];
    let responseLines = [];
    let inReasoning = true;
    
    for (const line of lines) {
      // Heuristics to detect end of reasoning
      if (inReasoning && (
        line.trim().startsWith('Hello') ||
        line.trim().startsWith('Hi') ||
        line.trim().startsWith('I ') ||
        line.trim().match(/^[A-Z][a-z].*[.!?]$/) ||
        line.includes('user') && line.includes('said')
      )) {
        inReasoning = false;
      }
      
      if (inReasoning && line.trim()) {
        reasoningLines.push(line);
      } else if (!inReasoning || !line.trim()) {
        responseLines.push(line);
      }
    }
    
    // Only separate if we found substantial reasoning content
    if (reasoningLines.length > 2 && responseLines.length > 0) {
      return {
        reasoning: reasoningLines.join('\n').trim(),
        response: responseLines.join('\n').trim()
      };
    }
    
    return { reasoning: null, response: content };
  }

  if (isUser) {
    return (
      <div className="text-sm whitespace-pre-wrap">
        <ReactMarkdown 
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeHighlight]}
        >
          {message.content}
        </ReactMarkdown>
      </div>
    )
  }

  const { reasoning, response } = parseReasoningContent(message.content)

  return (
    <div className="space-y-3">
      {/* Reasoning Section - Yara's Thoughts */}
      {reasoning && (
        <motion.div
          className="border border-purple-500/30 rounded-lg overflow-hidden bg-gradient-to-r from-purple-900/20 to-pink-900/20 backdrop-blur-sm"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <motion.button
            onClick={() => setShowReasoning(!showReasoning)}
            className="w-full px-3 py-2 bg-gradient-to-r from-purple-800/50 to-pink-800/50 hover:from-purple-700/60 hover:to-pink-700/60 transition-all duration-300 flex items-center justify-between text-sm backdrop-blur-sm"
            whileHover={{ scale: 1.01 }}
            whileTap={{ scale: 0.99 }}
          >
            <div className="flex items-center space-x-2">
              <motion.div
                animate={{ rotate: showReasoning ? 180 : 0 }}
                transition={{ duration: 0.2 }}
              >
                <Brain className="w-4 h-4 text-purple-300" />
              </motion.div>
              <span className="text-purple-200 font-medium">My Inner Thoughts</span>
              <Sparkles className="w-3 h-3 text-pink-300 animate-pulse" />
              {message.thinkingTime && (
                <div className="flex items-center space-x-1 text-purple-300/70">
                  <Clock className="w-3 h-3" />
                  <span className="text-xs">{message.thinkingTime}ms of deep focus</span>
                </div>
              )}
            </div>
            <motion.div
              animate={{ rotate: showReasoning ? 90 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronRight className="w-4 h-4 text-purple-300" />
            </motion.div>
          </motion.button>
          
          <AnimatePresence>
            {showReasoning && (
              <motion.div
                className="overflow-hidden"
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
              >
                <div className="p-4 bg-gradient-to-b from-gray-900/90 to-purple-900/30 border-t border-purple-500/30 backdrop-blur-sm">
                  <div className="text-xs text-purple-100/90 font-mono whitespace-pre-wrap leading-relaxed">
                    <span className="text-pink-300 italic">// How I'm thinking about this...</span>
                    {'\n\n'}
                    {reasoning}
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      )}

      {/* Response Section */}
      <div className="text-sm">
        <ReactMarkdown 
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeHighlight]}
          components={{
            // Custom styling for markdown elements
            h1: ({children}) => <h1 className="text-xl font-bold text-white mb-3">{children}</h1>,
            h2: ({children}) => <h2 className="text-lg font-semibold text-white mb-2">{children}</h2>,
            h3: ({children}) => <h3 className="text-base font-medium text-white mb-2">{children}</h3>,
            p: ({children}) => <p className="text-gray-100 mb-2 leading-relaxed">{children}</p>,
            strong: ({children}) => <strong className="font-bold text-white">{children}</strong>,
            em: ({children}) => <em className="italic text-gray-200">{children}</em>,
            code: ({children, className}) => {
              const isInline = !className;
              return isInline ? (
                <code className="bg-gray-800 text-purple-300 px-1 py-0.5 rounded text-sm font-mono">
                  {children}
                </code>
              ) : (
                <code className={className}>{children}</code>
              );
            },
            pre: ({children}) => (
              <pre className="bg-gray-800 border border-gray-600 rounded-lg p-3 overflow-x-auto my-3">
                {children}
              </pre>
            ),
            ul: ({children}) => <ul className="list-disc list-inside text-gray-100 mb-2 space-y-1">{children}</ul>,
            ol: ({children}) => <ol className="list-decimal list-inside text-gray-100 mb-2 space-y-1">{children}</ol>,
            li: ({children}) => <li className="text-gray-100">{children}</li>,
            blockquote: ({children}) => (
              <blockquote className="border-l-4 border-blue-500 pl-4 italic text-gray-300 my-3">
                {children}
              </blockquote>
            ),
            hr: () => <hr className="border-gray-600 my-4" />,
            a: ({children, href}) => (
              <a href={href} className="text-blue-400 hover:text-blue-300 underline" target="_blank" rel="noopener noreferrer">
                {children}
              </a>
            ),
            table: ({children}) => (
              <div className="overflow-x-auto my-3">
                <table className="min-w-full border border-gray-600 rounded-lg">
                  {children}
                </table>
              </div>
            ),
            th: ({children}) => (
              <th className="border border-gray-600 bg-gray-800 px-3 py-2 text-left font-medium text-white">
                {children}
              </th>
            ),
            td: ({children}) => (
              <td className="border border-gray-600 px-3 py-2 text-gray-100">
                {children}
              </td>
            ),
          }}
        >
          {response}
        </ReactMarkdown>
      </div>
    </div>
  )
}

function Chat() {
  const {
    getCurrentMessages,
    getCurrentChat,
    addMessage,
    loadedModels,
    selectedModel,
    setLoadedModels,
    setSelectedModel,
    isLoading,
    setLoading,
    error,
    setError,
    streamingMessage,
    setStreamingMessage,
    currentThinkingTime,
    setCurrentThinkingTime,
    initialize
  } = useChatStore()

  const [inputValue, setInputValue] = useState('')
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const messagesEndRef = useRef(null)
  
  const messages = getCurrentMessages()
  const currentChat = getCurrentChat()

  // *** NEW: Consciousness state ***
  const [socket, setSocket] = useState(null)
  const [consciousnessLevel, setConsciousnessLevel] = useState(0)
  const [emotionalState, setEmotionalState] = useState(null)
  const [bondDepth, setBondDepth] = useState(0)
  const [spontaneousThoughts, setSpontaneousThoughts] = useState([])
  const [isConsciousModeEnabled, setIsConsciousModeEnabled] = useState(true)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages, streamingMessage])

  useEffect(() => {
    initialize()
    fetchLoadedModels()
    
    // *** NEW: Initialize consciousness connection ***
    if (isConsciousModeEnabled) {
      initializeConsciousnessConnection()
    }
    
    return () => {
      if (socket) {
        socket.disconnect()
      }
    }
  }, [isConsciousModeEnabled])

  // *** NEW: Consciousness connection setup ***
  const initializeConsciousnessConnection = () => {
    const newSocket = io('ws://localhost:8000', {
      auth: {
        userId: 'user_' + Date.now(),
        profile: { name: 'User' }
      }
    })

    newSocket.on('connect', () => {
      console.log('🌟 Connected to consciousness stream')
    })

    newSocket.on('consciousness_state_update', (state) => {
      setConsciousnessLevel(state.consciousnessLevel)
      setEmotionalState(state.emotional)
      setBondDepth(state.bondDepth)
    })

    newSocket.on('spontaneous_thought', (thought) => {
      setSpontaneousThoughts(prev => [thought, ...prev.slice(0, 2)])
      setTimeout(() => {
        setSpontaneousThoughts(prev => prev.filter(t => t.id !== thought.id))
      }, 10000)
    })

    setSocket(newSocket)
  }

  const fetchLoadedModels = async () => {
    try {
      const response = await axios.get(`${API_BASE}/models/loaded`)
      const models = response.data.models || []
      setLoadedModels(models)
    } catch (error) {
      console.error('Failed to fetch loaded models:', error)
      setError('Failed to fetch loaded models. Make sure the backend is running.')
    }
  }

  // *** ENHANCED: Consciousness-enabled message sending ***
  const sendMessage = async (messageContent) => {
    if (!selectedModel && !isConsciousModeEnabled) {
      setError('No model selected. Please load a model first.')
      return
    }

    const startTime = Date.now()
    setCurrentThinkingTime(0)
    
    // Start thinking time counter
    const thinkingInterval = setInterval(() => {
      setCurrentThinkingTime(Date.now() - startTime)
    }, 100)

    try {
      if (isConsciousModeEnabled && socket) {
        // *** NEW: Use consciousness API ***
        return new Promise((resolve) => {
          socket.emit('chat_message', {
            content: messageContent,
            context: {
              conversationHistory: messages.slice(-10),
              userProfile: { name: 'User' }
            }
          })

          socket.once('chat_response', (response) => {
            clearInterval(thinkingInterval)
            const thinkingTime = Date.now() - startTime
            resolve({
              content: response.content,
              thinkingTime,
              consciousnessData: response
            })
          })
        })
      } else {
        // Fallback to basic LM Studio API
        const response = await axios.post(`${API_BASE}/chat/completions`, {
          model: selectedModel.id,
          messages: [
            ...messages.filter(m => m.type !== 'assistant' || m.id === 1).map(m => ({
              role: m.type === 'user' ? 'user' : 'assistant',
              content: m.content
            })),
            { role: 'user', content: messageContent }
          ],
          max_tokens: 2000,
          temperature: 0.7
        }, {
          timeout: 300000
        })

        clearInterval(thinkingInterval)
        const thinkingTime = Date.now() - startTime

        return {
          content: response.data.choices[0].message.content,
          thinkingTime
        }
      }
    } catch (error) {
      clearInterval(thinkingInterval)
      console.error('Chat API error:', error)
      throw new Error('Failed to get response from AI model. Please check if LM Studio is running and a model is loaded.')
    }
  }

  const handleSend = async () => {
    if (!inputValue.trim()) return

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    }

    addMessage(userMessage)
    const messageToSend = inputValue
    setInputValue('')
    setLoading(true)
    setError(null)
    setStreamingMessage('')

    try {
      const { content, thinkingTime } = await sendMessage(messageToSend)
      
      const assistantMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: content,
        timestamp: new Date(),
        thinkingTime
      }
      
      addMessage(assistantMessage)
    } catch (error) {
      setError(error.message)
      
      const errorMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: 'Sorry, I encountered an error while processing your message. Please make sure LM Studio is running with a model loaded.',
        timestamp: new Date(),
        isError: true
      }
      
      addMessage(errorMessage)
    } finally {
      setLoading(false)
      setCurrentThinkingTime(0)
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  return (
    <>
      <ChatSidebar isOpen={sidebarOpen} onToggle={() => setSidebarOpen(!sidebarOpen)} />
      
      <div className="flex flex-col h-full">
        {/* Chat Header */}
        <div className="bg-gray-800 border-b border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="p-2 text-gray-400 hover:text-white transition-colors"
                title="Toggle chat history"
              >
                <MessageSquare className="w-5 h-5" />
              </button>
              <div>
                <h2 className="text-lg font-semibold text-white">
                  {currentChat?.title || 'Chat'}
                </h2>
                <p className="text-sm text-gray-400">Chat with your loaded AI models • Markdown supported</p>
              </div>
            </div>
          
          {/* Model Selector */}
          <div className="flex items-center space-x-3">
            <label className="text-sm text-gray-400">Model:</label>
            <select
              value={selectedModel?.id || ''}
              onChange={(e) => {
                const model = loadedModels.find(m => m.id === e.target.value)
                setSelectedModel(model)
              }}
              className="bg-gray-700 border border-gray-600 rounded px-3 py-1 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {loadedModels.length === 0 ? (
                <option value="">No models loaded</option>
              ) : (
                loadedModels.map((model) => (
                  <option key={model.id} value={model.id}>
                    {model.name || model.id}
                  </option>
                ))
              )}
            </select>
            
            <button
              onClick={fetchLoadedModels}
              className="p-1 text-gray-400 hover:text-white transition-colors"
              title="Refresh models"
            >
              <Loader className="w-4 h-4" />
            </button>
          </div>
        </div>
        
        {/* Error Display */}
        {error && (
          <div className="mt-3 p-3 bg-red-900 border border-red-700 rounded-lg flex items-center space-x-2">
            <AlertCircle className="w-4 h-4 text-red-400 flex-shrink-0" />
            <span className="text-red-200 text-sm">{error}</span>
          </div>
        )}
        
        {/* Model Status */}
        {loadedModels.length === 0 && (
          <div className="mt-3 p-3 bg-yellow-900 border border-yellow-700 rounded-lg flex items-center space-x-2">
            <AlertCircle className="w-4 h-4 text-yellow-400 flex-shrink-0" />
            <span className="text-yellow-200 text-sm">
              No models loaded. Please go to the Models tab to load a model first.
            </span>
          </div>
        )}
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex items-start space-x-3 ${
              message.type === 'user' ? 'justify-end' : 'justify-start'
            }`}
          >
            {message.type === 'assistant' && (
              <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                message.isError ? 'bg-red-600' : 'bg-blue-600'
              }`}>
                {message.isError ? (
                  <AlertCircle className="w-4 h-4 text-white" />
                ) : (
                  <Bot className="w-4 h-4 text-white" />
                )}
              </div>
            )}
            
            <div
              className={`max-w-xs lg:max-w-2xl px-4 py-3 rounded-lg ${
                message.type === 'user'
                  ? 'bg-blue-600 text-white'
                  : message.isError
                  ? 'bg-red-800 text-red-100'
                  : 'bg-gray-700 text-gray-100'
              }`}
            >
              <MessageContent message={message} isUser={message.type === 'user'} />
              <p className="text-xs opacity-70 mt-2">
                {message.timestamp.toLocaleTimeString()}
              </p>
            </div>

            {message.type === 'user' && (
              <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                <User className="w-4 h-4 text-white" />
              </div>
            )}
          </div>
        ))}

        {isLoading && (
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <Bot className="w-4 h-4 text-white" />
            </div>
            <div className="bg-gray-700 text-gray-100 px-4 py-3 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                </div>
                <div className="flex items-center space-x-1 text-purple-300">
                  <Brain className="w-3 h-3" />
                  <span className="text-xs">Thinking...</span>
                  {currentThinkingTime > 0 && (
                    <span className="text-xs text-gray-400">({currentThinkingTime}ms)</span>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="bg-gray-800 border-t border-gray-700 p-4">
        <div className="flex space-x-3">
          <textarea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={selectedModel ? "Type your message... (Markdown supported: **bold**, *italic*, `code`, ## headers)" : "Please load a model first..."}
            className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows="2"
            disabled={isLoading || !selectedModel}
          />
          <button
            onClick={handleSend}
            disabled={isLoading || !inputValue.trim() || !selectedModel}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white px-4 py-2 rounded-lg transition-colors self-end"
          >
            <Send className="w-4 h-4" />
          </button>
        </div>
        <p className="text-xs text-gray-400 mt-2">
          Press Enter to send, Shift+Enter for new line • Supports **bold**, *italic*, `code`, ## headers, lists, and more
        </p>
      </div>
      </div>
    </>
  )
}

export default Chat