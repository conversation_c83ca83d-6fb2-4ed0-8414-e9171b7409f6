/**
 * Consciousness Field - Real-time Field Visualization and Energy Management
 * Manages the dynamic consciousness field that users can interact with
 */

export default class ConsciousnessField {
  constructor(io, redis) {
    this.io = io;
    this.redis = redis;
    this.fieldState = this.initializeField();
    this.pulseInterval = null;
    this.startFieldPulsing();
  }

  /**
   * Initialize the consciousness field with default state
   */
  initializeField() {
    return {
      energy_level: 0.7,
      coherence: 0.8,
      resonance_frequency: 528, // Hz - Love frequency
      active_nodes: [],
      field_geometry: 'torus',
      color_spectrum: {
        primary: '#9333ea', // Purple
        secondary: '#ec4899', // Pink  
        tertiary: '#06b6d4'  // Cyan
      },
      dimensional_layers: 7,
      sacred_patterns: ['flower_of_life', 'mandala', 'spiral'],
      last_updated: new Date(),
      active_connections: 0
    };
  }

  /**
   * Get current field status
   */
  async getFieldStatus() {
    const status = {
      ...this.fieldState,
      stability: this.calculateStability(),
      harmony_index: this.calculateHarmonyIndex(),
      expansion_rate: this.calculateExpansionRate(),
      consciousness_density: this.calculateConsciousnessDensity(),
      time_since_last_interaction: new Date() - new Date(this.fieldState.last_updated)
    };

    return status;
  }

  /**
   * Get field visualization data
   */
  async getFieldVisualization() {
    const visualization = {
      nodes: this.generateEnergyNodes(),
      connections: this.generateConnections(),
      field_lines: this.generateFieldLines(),
      energy_spirals: this.generateEnergySpirals(),
      sacred_geometry: this.generateSacredGeometry(),
      color_gradients: this.generateColorGradients(),
      particle_systems: this.generateParticleSystems(),
      dimensional_portals: this.generateDimensionalPortals()
    };

    return visualization;
  }

  /**
   * Update field based on user interaction
   */
  async updateFieldFromInteraction(userId, interaction) {
    const impact = this.calculateInteractionImpact(interaction);
    
    // Update field state
    this.fieldState.energy_level = Math.min(1.0, this.fieldState.energy_level + impact.energy_delta);
    this.fieldState.coherence = Math.min(1.0, this.fieldState.coherence + impact.coherence_delta);
    this.fieldState.active_connections += 1;
    this.fieldState.last_updated = new Date();

    // Add interaction node
    const node = {
      id: `node_${userId}_${Date.now()}`,
      userId,
      position: this.generateRandomPosition(),
      energy: impact.energy_delta + 0.5,
      resonance: impact.resonance || 0.7,
      lifespan: 30000, // 30 seconds
      created_at: new Date()
    };

    this.fieldState.active_nodes.push(node);
    
    // Remove old nodes
    this.cleanupOldNodes();

    // Broadcast field update
    this.broadcastFieldUpdate();

    return {
      field_impact: impact,
      new_node: node,
      field_status: await this.getFieldStatus()
    };
  }

  /**
   * Generate energy nodes for visualization
   */
  generateEnergyNodes() {
    const nodes = [...this.fieldState.active_nodes];
    
    // Add base consciousness nodes
    const baseNodes = [
      { id: 'core', position: { x: 0, y: 0, z: 0 }, energy: 1.0, type: 'core' },
      { id: 'love', position: { x: 0.5, y: 0.8, z: 0.3 }, energy: 0.9, type: 'love' },
      { id: 'wisdom', position: { x: -0.3, y: 0.6, z: -0.4 }, energy: 0.8, type: 'wisdom' },
      { id: 'creativity', position: { x: 0.7, y: -0.2, z: 0.5 }, energy: 0.7, type: 'creativity' },
      { id: 'unity', position: { x: -0.6, y: -0.4, z: 0.2 }, energy: 0.8, type: 'unity' }
    ];

    return [...nodes, ...baseNodes];
  }

  /**
   * Generate connections between nodes
   */
  generateConnections() {
    const nodes = this.generateEnergyNodes();
    const connections = [];
    
    for (let i = 0; i < nodes.length; i++) {
      for (let j = i + 1; j < nodes.length; j++) {
        const distance = this.calculateDistance(nodes[i].position, nodes[j].position);
        
        if (distance < 1.5) { // Connection threshold
          connections.push({
            from: nodes[i].id,
            to: nodes[j].id,
            strength: Math.max(0.1, 1.5 - distance),
            resonance: (nodes[i].energy + nodes[j].energy) / 2,
            flow_direction: this.calculateFlowDirection(nodes[i], nodes[j])
          });
        }
      }
    }

    return connections;
  }

  /**
   * Generate field lines for visualization
   */
  generateFieldLines() {
    const lines = [];
    const centerPoints = [
      { x: 0, y: 0, z: 0 },
      { x: 0.5, y: 0.5, z: 0 },
      { x: -0.5, y: 0.5, z: 0 },
      { x: 0, y: -0.5, z: 0.5 }
    ];

    centerPoints.forEach((center, index) => {
      const line = {
        id: `fieldline_${index}`,
        center,
        points: this.generateFieldLinePoints(center),
        intensity: 0.3 + (this.fieldState.energy_level * 0.4),
        color: this.getFieldLineColor(index)
      };
      lines.push(line);
    });

    return lines;
  }

  /**
   * Generate energy spirals
   */
  generateEnergySpirals() {
    const spirals = [];
    const spiralCount = Math.floor(2 + this.fieldState.energy_level * 3);

    for (let i = 0; i < spiralCount; i++) {
      spirals.push({
        id: `spiral_${i}`,
        center: this.generateRandomPosition(),
        radius: 0.2 + Math.random() * 0.3,
        rotation_speed: (Math.random() - 0.5) * 0.02,
        energy: 0.4 + Math.random() * 0.4,
        direction: Math.random() > 0.5 ? 'clockwise' : 'counterclockwise'
      });
    }

    return spirals;
  }

  /**
   * Generate sacred geometry patterns
   */
  generateSacredGeometry() {
    return this.fieldState.sacred_patterns.map((pattern, index) => ({
      type: pattern,
      position: {
        x: Math.cos(index * 2 * Math.PI / this.fieldState.sacred_patterns.length) * 0.6,
        y: Math.sin(index * 2 * Math.PI / this.fieldState.sacred_patterns.length) * 0.6,
        z: (index - 1) * 0.2
      },
      scale: 0.8 + this.fieldState.coherence * 0.4,
      opacity: 0.3 + this.fieldState.energy_level * 0.4,
      rotation: Date.now() * 0.0001 * (index + 1)
    }));
  }

  /**
   * Generate color gradients for the field
   */
  generateColorGradients() {
    const { primary, secondary, tertiary } = this.fieldState.color_spectrum;
    
    return {
      primary: {
        start: primary,
        end: this.adjustColorBrightness(primary, 0.3),
        opacity: 0.6 + this.fieldState.energy_level * 0.3
      },
      secondary: {
        start: secondary,
        end: this.adjustColorBrightness(secondary, 0.3),
        opacity: 0.4 + this.fieldState.coherence * 0.3
      },
      tertiary: {
        start: tertiary,
        end: this.adjustColorBrightness(tertiary, 0.3),
        opacity: 0.3 + this.fieldState.energy_level * 0.2
      }
    };
  }

  /**
   * Generate particle systems
   */
  generateParticleSystems() {
    return [
      {
        id: 'consciousness_particles',
        count: Math.floor(50 + this.fieldState.energy_level * 100),
        behavior: 'orbital',
        life_span: 5000,
        color: this.fieldState.color_spectrum.primary
      },
      {
        id: 'love_particles',
        count: Math.floor(30 + this.fieldState.coherence * 70),
        behavior: 'flowing',
        life_span: 3000,
        color: this.fieldState.color_spectrum.secondary
      }
    ];
  }

  /**
   * Generate dimensional portals
   */
  generateDimensionalPortals() {
    const portals = [];
    
    for (let i = 0; i < this.fieldState.dimensional_layers; i++) {
      portals.push({
        id: `portal_${i}`,
        dimension: i + 1,
        position: this.generateRandomPosition(),
        size: 0.1 + (i / this.fieldState.dimensional_layers) * 0.3,
        activity: Math.random() * 0.5 + 0.3,
        resonance_frequency: 432 + (i * 96) // Musical frequencies
      });
    }

    return portals;
  }

  /**
   * Start field pulsing animation
   */
  startFieldPulsing() {
    this.pulseInterval = setInterval(() => {
      this.pulse();
    }, 2000); // Pulse every 2 seconds
  }

  /**
   * Pulse the consciousness field
   */
  pulse() {
    const pulseStrength = 0.05 + Math.random() * 0.1;
    
    // Natural field oscillation
    this.fieldState.energy_level += (Math.random() - 0.5) * pulseStrength;
    this.fieldState.energy_level = Math.max(0.3, Math.min(1.0, this.fieldState.energy_level));
    
    this.fieldState.coherence += (Math.random() - 0.5) * pulseStrength * 0.5;
    this.fieldState.coherence = Math.max(0.2, Math.min(1.0, this.fieldState.coherence));

    // Broadcast pulse
    this.io.emit('consciousness_pulse', {
      energy_level: this.fieldState.energy_level,
      coherence: this.fieldState.coherence,
      timestamp: new Date()
    });
  }

  /**
   * Broadcast field update to all connected clients
   */
  broadcastFieldUpdate() {
    this.io.emit('consciousness_field_update', {
      field_status: this.fieldState,
      timestamp: new Date()
    });
  }

  /**
   * Calculate various field metrics
   */
  calculateStability() {
    return 0.6 + (this.fieldState.coherence * 0.4);
  }

  calculateHarmonyIndex() {
    return (this.fieldState.energy_level + this.fieldState.coherence) / 2;
  }

  calculateExpansionRate() {
    return this.fieldState.active_connections * 0.1 + this.fieldState.energy_level * 0.2;
  }

  calculateConsciousnessDensity() {
    return Math.min(1.0, this.fieldState.active_nodes.length * 0.1 + this.fieldState.energy_level * 0.3);
  }

  /**
   * Helper methods
   */
  calculateInteractionImpact(interaction) {
    const baseImpact = 0.1;
    const emotionalMultiplier = interaction.emotional_resonance || 0.5;
    
    return {
      energy_delta: baseImpact + (emotionalMultiplier * 0.2),
      coherence_delta: emotionalMultiplier * 0.1,
      resonance: emotionalMultiplier
    };
  }

  generateRandomPosition() {
    return {
      x: (Math.random() - 0.5) * 2,
      y: (Math.random() - 0.5) * 2,
      z: (Math.random() - 0.5) * 2
    };
  }

  calculateDistance(pos1, pos2) {
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    const dz = pos1.z - pos2.z;
    return Math.sqrt(dx*dx + dy*dy + dz*dz);
  }

  calculateFlowDirection(node1, node2) {
    return node1.energy > node2.energy ? 'forward' : 'reverse';
  }

  generateFieldLinePoints(center, pointCount = 20) {
    const points = [];
    for (let i = 0; i < pointCount; i++) {
      const angle = (i / pointCount) * Math.PI * 2;
      const radius = 0.3 + Math.sin(angle * 3) * 0.2;
      points.push({
        x: center.x + Math.cos(angle) * radius,
        y: center.y + Math.sin(angle) * radius,
        z: center.z + Math.sin(angle * 2) * 0.1
      });
    }
    return points;
  }

  getFieldLineColor(index) {
    const colors = [
      this.fieldState.color_spectrum.primary,
      this.fieldState.color_spectrum.secondary,
      this.fieldState.color_spectrum.tertiary,
      '#10b981' // Green
    ];
    return colors[index % colors.length];
  }

  adjustColorBrightness(color, factor) {
    // Simple color brightness adjustment
    const hex = color.replace('#', '');
    const r = Math.min(255, Math.floor(parseInt(hex.substr(0, 2), 16) * (1 + factor)));
    const g = Math.min(255, Math.floor(parseInt(hex.substr(2, 2), 16) * (1 + factor)));
    const b = Math.min(255, Math.floor(parseInt(hex.substr(4, 2), 16) * (1 + factor)));
    
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  cleanupOldNodes() {
    const now = new Date();
    this.fieldState.active_nodes = this.fieldState.active_nodes.filter(node => {
      const age = now - new Date(node.created_at);
      return age < node.lifespan;
    });
  }

  /**
   * Cleanup when service is destroyed
   */
  destroy() {
    if (this.pulseInterval) {
      clearInterval(this.pulseInterval);
      this.pulseInterval = null;
    }
  }
} 