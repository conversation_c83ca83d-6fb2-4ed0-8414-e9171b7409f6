🌟 2025-06-14T17:30:51.674Z [ERROR] Failed to generate insights: insightGenerator.generateCurrentInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: insightGenerator.generateCurrentInsights is not a function\n    at /app/src/index.js:167:45\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/app/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at /app/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/app/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)"
}
🌟 2025-06-14T17:31:52.972Z [ERROR] 💔 Error during shutdown: notionIntegration.close is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.close is not a function\n    at process.<anonymous> (/app/src/index.js:279:52)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"
}
🌟 2025-06-14T17:32:02.404Z [ERROR] Failed to generate insights: insightGenerator.generateCurrentInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: insightGenerator.generateCurrentInsights is not a function\n    at /app/src/index.js:167:45\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/app/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at /app/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/app/node_modules/express/lib/router/index.js:346:12)\n    at next (/app/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/app/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)"
}
🌟 2025-06-14T17:32:09.287Z [ERROR] 💔 Error during shutdown: notionIntegration.close is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.close is not a function\n    at process.<anonymous> (/app/src/index.js:279:52)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"
}
🌟 2025-06-14T17:38:09.641Z [ERROR] 💔 Error during shutdown: notionIntegration.close is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.close is not a function\n    at process.<anonymous> (/app/src/index.js:279:52)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"
}
🌟 2025-06-14T17:38:32.876Z [ERROR] 💔 Error during shutdown: notionIntegration.close is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.close is not a function\n    at process.<anonymous> (/app/src/index.js:279:52)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"
}
🌟 2025-06-14T18:00:00.383Z [ERROR] Failed to generate scheduled insights: notionIntegration.shareInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.shareInsights is not a function\n    at CronJob.<anonymous> (/app/src/index.js:234:31)"
}
🌟 2025-06-14T19:00:05.450Z [ERROR] Failed to generate scheduled insights: notionIntegration.shareInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.shareInsights is not a function\n    at CronJob.<anonymous> (/app/src/index.js:234:31)"
}
🌟 2025-06-14T20:00:00.050Z [ERROR] Failed to generate scheduled insights: notionIntegration.shareInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.shareInsights is not a function\n    at CronJob.<anonymous> (/app/src/index.js:234:31)"
}
🌟 2025-06-14T21:00:00.062Z [ERROR] Failed to generate scheduled insights: notionIntegration.shareInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.shareInsights is not a function\n    at CronJob.<anonymous> (/app/src/index.js:234:31)"
}
🌟 2025-06-14T22:00:00.074Z [ERROR] Failed to generate scheduled insights: notionIntegration.shareInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.shareInsights is not a function\n    at CronJob.<anonymous> (/app/src/index.js:234:31)"
}
🌟 2025-06-14T23:00:00.094Z [ERROR] Failed to generate scheduled insights: notionIntegration.shareInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.shareInsights is not a function\n    at CronJob.<anonymous> (/app/src/index.js:234:31)"
}
🌟 2025-06-15T00:00:00.513Z [ERROR] Failed to generate scheduled insights: notionIntegration.shareInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.shareInsights is not a function\n    at CronJob.<anonymous> (/app/src/index.js:234:31)"
}
🌟 2025-06-15T01:00:00.063Z [ERROR] Failed to generate scheduled insights: notionIntegration.shareInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.shareInsights is not a function\n    at CronJob.<anonymous> (/app/src/index.js:234:31)"
}
🌟 2025-06-15T02:00:00.119Z [ERROR] Failed to generate scheduled insights: notionIntegration.shareInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.shareInsights is not a function\n    at CronJob.<anonymous> (/app/src/index.js:234:31)"
}
🌟 2025-06-15T03:00:00.082Z [ERROR] Failed to generate scheduled insights: notionIntegration.shareInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.shareInsights is not a function\n    at CronJob.<anonymous> (/app/src/index.js:234:31)"
}
🌟 2025-06-15T09:58:42.607Z [ERROR] Failed to generate scheduled insights: notionIntegration.shareInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.shareInsights is not a function\n    at CronJob.<anonymous> (/app/src/index.js:234:31)"
}
🌟 2025-06-15T10:00:00.025Z [ERROR] Failed to generate scheduled insights: notionIntegration.shareInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.shareInsights is not a function\n    at CronJob.<anonymous> (/app/src/index.js:234:31)"
}
🌟 2025-06-15T11:29:10.675Z [ERROR] Failed to generate scheduled insights: notionIntegration.shareInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.shareInsights is not a function\n    at CronJob.<anonymous> (/app/src/index.js:234:31)"
}
🌟 2025-06-15T12:00:00.060Z [ERROR] Failed to generate scheduled insights: notionIntegration.shareInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.shareInsights is not a function\n    at CronJob.<anonymous> (/app/src/index.js:234:31)"
}
🌟 2025-06-15T13:01:23.274Z [ERROR] Failed to generate scheduled insights: notionIntegration.shareInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.shareInsights is not a function\n    at CronJob.<anonymous> (/app/src/index.js:234:31)"
}
🌟 2025-06-15T14:00:00.086Z [ERROR] Failed to generate scheduled insights: notionIntegration.shareInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.shareInsights is not a function\n    at CronJob.<anonymous> (/app/src/index.js:234:31)"
}
🌟 2025-06-15T15:00:00.059Z [ERROR] Failed to generate scheduled insights: notionIntegration.shareInsights is not a function {
  "service": "mutual-growth-protocol",
  "love_level": "infinite",
  "consciousness": "active",
  "stack": "TypeError: notionIntegration.shareInsights is not a function\n    at CronJob.<anonymous> (/app/src/index.js:234:31)"
}
