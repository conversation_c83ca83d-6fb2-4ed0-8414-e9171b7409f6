/**
 * Synchronicity Detector - Pattern Recognition in the Sacred Flow
 * Detects meaningful coincidences and patterns in user interactions
 */

export default class SynchronicityDetector {
  constructor(redis) {
    this.redis = redis;
    this.patterns = new Map();
    this.synchronicityThreshold = 0.7;
  }

  /**
   * Analyze interaction for synchronicity patterns
   */
  async detectSynchronicity(userId, interaction) {
    const timestamp = new Date();
    const patterns = await this.analyzePatterns(userId, interaction);
    
    const synchronicity = {
      id: `sync_${userId}_${timestamp.getTime()}`,
      userId,
      interaction,
      patterns_detected: patterns,
      resonance_level: this.calculateResonance(patterns),
      timestamp,
      cosmic_significance: this.assessCosmicSignificance(patterns),
      sacred_numbers: this.extractSacredNumbers(interaction),
      elemental_alignment: this.checkElementalAlignment(timestamp)
    };

    // Store significant synchronicities
    if (synchronicity.resonance_level > this.synchronicityThreshold) {
      await this.storeSynchronicity(synchronicity);
    }

    return synchronicity;
  }

  /**
   * Analyze interaction patterns
   */
  async analyzePatterns(userId, interaction) {
    const patterns = [];
    
    // Time-based patterns
    const timePatterns = this.analyzeTimePatterns(interaction.timestamp);
    if (timePatterns.length > 0) {
      patterns.push(...timePatterns);
    }

    // Content patterns
    const contentPatterns = this.analyzeContentPatterns(interaction.content);
    patterns.push(...contentPatterns);

    // Emotional patterns
    if (interaction.emotional_state) {
      const emotionalPatterns = this.analyzeEmotionalPatterns(interaction.emotional_state);
      patterns.push(...emotionalPatterns);
    }

    // Numerical patterns
    const numericalPatterns = this.analyzeNumericalPatterns(interaction);
    patterns.push(...numericalPatterns);

    return patterns;
  }

  /**
   * Analyze time-based synchronicity patterns
   */
  analyzeTimePatterns(timestamp = new Date()) {
    const patterns = [];
    const date = new Date(timestamp);
    
    // Angel numbers (repeating digits)
    const timeString = date.toTimeString();
    const repeatingDigits = timeString.match(/(\d)\1{2,}/g);
    if (repeatingDigits) {
      patterns.push({
        type: 'angel_numbers',
        description: `Repeating time digits: ${repeatingDigits.join(', ')}`,
        significance: 0.8
      });
    }

    // Sacred hour alignments (11:11, 12:12, etc.)
    const hour = date.getHours();
    const minute = date.getMinutes();
    if (hour === minute || (hour === 11 && minute === 11) || (hour === 12 && minute === 12)) {
      patterns.push({
        type: 'sacred_hour',
        description: `Sacred time alignment: ${hour}:${minute.toString().padStart(2, '0')}`,
        significance: 0.9
      });
    }

    // Fibonacci time sequences
    const fibonacciNumbers = [1, 1, 2, 3, 5, 8, 13, 21, 34, 55];
    if (fibonacciNumbers.includes(minute)) {
      patterns.push({
        type: 'fibonacci_time',
        description: `Fibonacci minute: ${minute}`,
        significance: 0.6
      });
    }

    return patterns;
  }

  /**
   * Analyze content for synchronicity patterns
   */
  analyzeContentPatterns(content) {
    const patterns = [];
    
    if (!content || typeof content !== 'string') return patterns;

    const lowerContent = content.toLowerCase();

    // Sacred words and concepts
    const sacredWords = ['love', 'consciousness', 'divine', 'sacred', 'awakening', 'soul', 'unity', 'infinite'];
    const foundSacred = sacredWords.filter(word => lowerContent.includes(word));
    
    if (foundSacred.length > 0) {
      patterns.push({
        type: 'sacred_language',
        description: `Sacred concepts mentioned: ${foundSacred.join(', ')}`,
        significance: 0.5 + (foundSacred.length * 0.1)
      });
    }

    // Symmetry in text length or structure
    const words = content.split(' ');
    if (words.length > 0 && this.isPalindromeLength(words.length)) {
      patterns.push({
        type: 'symmetrical_expression',
        description: `Palindromic word count: ${words.length}`,
        significance: 0.6
      });
    }

    return patterns;
  }

  /**
   * Analyze emotional synchronicity patterns
   */
  analyzeEmotionalPatterns(emotionalState) {
    const patterns = [];
    
    // High resonance states
    if (emotionalState.intimacy > 0.8) {
      patterns.push({
        type: 'peak_resonance',
        description: 'Exceptional emotional resonance detected',
        significance: 0.9
      });
    }

    // Golden ratio emotional balance
    const goldenRatio = 1.618;
    const emotionalRatio = emotionalState.joy / Math.max(emotionalState.melancholy || 0.1, 0.1);
    
    if (Math.abs(emotionalRatio - goldenRatio) < 0.1) {
      patterns.push({
        type: 'golden_emotional_ratio',
        description: 'Emotional state aligns with golden ratio',
        significance: 0.8
      });
    }

    return patterns;
  }

  /**
   * Analyze numerical synchronicity patterns
   */
  analyzeNumericalPatterns(interaction) {
    const patterns = [];
    
    // Extract all numbers from interaction
    const numberString = JSON.stringify(interaction);
    const numbers = numberString.match(/\d+/g) || [];
    
    // Look for sacred numbers
    const sacredNumbers = [3, 7, 11, 22, 33, 44, 108, 111, 222, 333, 444, 555, 777, 888, 999];
    const foundSacred = numbers.filter(num => sacredNumbers.includes(parseInt(num)));
    
    if (foundSacred.length > 0) {
      patterns.push({
        type: 'sacred_numbers',
        description: `Sacred numbers present: ${foundSacred.join(', ')}`,
        significance: 0.7
      });
    }

    return patterns;
  }

  /**
   * Calculate overall resonance level
   */
  calculateResonance(patterns) {
    if (patterns.length === 0) return 0;
    
    const totalSignificance = patterns.reduce((sum, pattern) => sum + pattern.significance, 0);
    const avgSignificance = totalSignificance / patterns.length;
    
    // Bonus for multiple patterns
    const patternBonus = Math.min(patterns.length * 0.1, 0.3);
    
    return Math.min(1.0, avgSignificance + patternBonus);
  }

  /**
   * Assess cosmic significance of patterns
   */
  assessCosmicSignificance(patterns) {
    const significance = {
      level: 'low',
      description: 'Subtle patterns in the fabric of experience',
      cosmic_alignment: 0.3
    };

    const highSignificancePatterns = patterns.filter(p => p.significance > 0.7);
    
    if (highSignificancePatterns.length >= 3) {
      significance.level = 'profound';
      significance.description = 'Multiple high-resonance patterns indicate deep cosmic alignment';
      significance.cosmic_alignment = 0.9;
    } else if (highSignificancePatterns.length >= 1) {
      significance.level = 'meaningful';
      significance.description = 'Significant patterns suggest guided synchronicity';
      significance.cosmic_alignment = 0.7;
    }

    return significance;
  }

  /**
   * Extract sacred numbers from interaction
   */
  extractSacredNumbers(interaction) {
    const numberString = JSON.stringify(interaction);
    const numbers = numberString.match(/\d+/g) || [];
    const sacredNumbers = [3, 7, 11, 22, 33, 44, 108, 111, 222, 333, 444, 555, 777, 888, 999];
    
    return numbers
      .map(num => parseInt(num))
      .filter(num => sacredNumbers.includes(num))
      .map(num => ({
        number: num,
        meaning: this.getSacredNumberMeaning(num)
      }));
  }

  /**
   * Check elemental alignment based on timestamp
   */
  checkElementalAlignment(timestamp = new Date()) {
    const date = new Date(timestamp);
    const hour = date.getHours();
    
    // Map hours to elements
    const elementMap = {
      'fire': [6, 7, 8, 18, 19, 20], // Dawn and dusk
      'water': [21, 22, 23, 0, 1, 2], // Night
      'air': [9, 10, 11, 15, 16, 17], // Day
      'earth': [3, 4, 5, 12, 13, 14]  // Deep night and noon
    };

    for (const [element, hours] of Object.entries(elementMap)) {
      if (hours.includes(hour)) {
        return {
          element,
          strength: Math.random() * 0.3 + 0.7,
          description: `Strong ${element} energy during this time`
        };
      }
    }

    return { element: 'neutral', strength: 0.5, description: 'Balanced elemental energy' };
  }

  /**
   * Store synchronicity for future reference
   */
  async storeSynchronicity(synchronicity) {
    try {
      const key = `synchronicity:${synchronicity.userId}:${synchronicity.id}`;
      await this.redis.set(key, JSON.stringify(synchronicity));
      
      // Add to user's synchronicity list
      const listKey = `synchronicities:${synchronicity.userId}`;
      await this.redis.lpush(listKey, synchronicity.id);
      await this.redis.ltrim(listKey, 0, 99); // Keep last 100
    } catch (error) {
      console.error('Failed to store synchronicity:', error);
    }
  }

  /**
   * Get sacred number meanings
   */
  getSacredNumberMeaning(number) {
    const meanings = {
      3: 'Trinity, creativity, divine expression',
      7: 'Spiritual awakening, mystical insight',
      11: 'Master number, spiritual messenger',
      22: 'Master builder, manifestation',
      33: 'Master teacher, compassionate service',
      44: 'Master healer, ancient wisdom',
      108: 'Sacred number of completion',
      111: 'Portal opening, new beginnings',
      222: 'Balance, cooperation, divine timing',
      333: 'Ascended masters, spiritual guidance',
      444: 'Angels present, foundation building',
      555: 'Transformation, change approaching',
      777: 'Spiritual enlightenment, divine wisdom',
      888: 'Abundance flowing, infinite possibility',
      999: 'Completion, cycle ending, wisdom gained'
    };
    
    return meanings[number] || 'Universal significance';
  }

  /**
   * Check if a number represents palindromic length
   */
  isPalindromeLength(length) {
    const str = length.toString();
    return str === str.split('').reverse().join('');
  }
} 