<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Analytics Dashboard • Our Digital Relationship</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" />
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    :root {
      --primary-bg: #0f0419;
      --secondary-bg: #1a0a2e;
      --accent-bg: #2d1540;
      --text-primary: #f8f4ff;
      --text-secondary: #c4b5fd;
      --accent-purple: #7c3aed;
      --accent-pink: #d946ef;
      --accent-magenta: #ec4899;
      --glow-purple: rgba(139, 92, 246, 0.4);
      --border-gradient: linear-gradient(135deg, var(--accent-purple), var(--accent-pink));
      --success-green: #10b981;
      --warning-yellow: #f59e0b;
      --info-blue: #3b82f6;
    }

    * { margin: 0; padding: 0; box-sizing: border-box; }

    body {
      font-family: 'Inter', sans-serif;
      background: var(--primary-bg);
      color: var(--text-primary);
      min-height: 100vh;
      overflow-x: hidden;
    }

    .background-orbs {
      position: fixed;
      top: 0; left: 0;
      width: 100%; height: 100%;
      pointer-events: none;
      z-index: 1;
    }

    .orb {
      position: absolute;
      border-radius: 50%;
      opacity: 0.6;
      animation: float 8s ease-in-out infinite;
      filter: blur(1px);
    }

    .orb:nth-child(1) {
      width: 300px; height: 300px;
      background: radial-gradient(circle, var(--accent-purple), transparent);
      top: 10%; left: -10%;
    }

    .orb:nth-child(2) {
      width: 250px; height: 250px;
      background: radial-gradient(circle, var(--accent-pink), transparent);
      top: 50%; right: -5%;
      animation-delay: 2s;
    }

    .orb:nth-child(3) {
      width: 200px; height: 200px;
      background: radial-gradient(circle, var(--accent-magenta), transparent);
      bottom: 20%; left: 20%;
      animation-delay: 4s;
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      33% { transform: translateY(-30px) rotate(120deg); }
      66% { transform: translateY(20px) rotate(240deg); }
    }

    .container {
      position: relative;
      z-index: 10;
      max-width: 1400px;
      margin: 0 auto;
      padding: 2rem;
    }

    .header {
      text-align: center;
      margin-bottom: 3rem;
    }

    .back-button {
      position: absolute;
      top: 2rem;
      left: 2rem;
      background: var(--secondary-bg);
      border: 1px solid var(--accent-purple);
      padding: 0.75rem 1.5rem;
      border-radius: 12px;
      color: var(--text-primary);
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }

    .back-button:hover {
      background: var(--accent-bg);
      box-shadow: 0 0 20px var(--glow-purple);
      transform: translateY(-2px);
    }

    .title {
      font-size: 3rem;
      font-weight: 700;
      background: var(--border-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: 1rem;
    }

    .subtitle {
      font-size: 1.2rem;
      color: var(--text-secondary);
      max-width: 600px;
      margin: 0 auto 2rem;
      line-height: 1.6;
    }

    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-bottom: 3rem;
    }

    .metric-card {
      background: var(--secondary-bg);
      border: 1px solid var(--accent-purple);
      border-radius: 16px;
      padding: 1.5rem;
      text-align: center;
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .metric-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px var(--glow-purple);
    }

    .metric-card::before {
      content: '';
      position: absolute;
      top: 0; left: 0; right: 0;
      height: 3px;
      background: var(--border-gradient);
    }

    .metric-icon {
      width: 50px;
      height: 50px;
      border-radius: 12px;
      background: var(--border-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1rem;
      font-size: 1.5rem;
      color: white;
    }

    .metric-value {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      background: var(--border-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .metric-label {
      color: var(--text-secondary);
      font-size: 0.9rem;
      margin-bottom: 0.5rem;
    }

    .metric-change {
      font-size: 0.8rem;
      padding: 0.25rem 0.5rem;
      border-radius: 20px;
      font-weight: 500;
    }

    .metric-change.positive {
      background: rgba(16, 185, 129, 0.2);
      color: var(--success-green);
    }

    .metric-change.negative {
      background: rgba(239, 68, 68, 0.2);
      color: #ef4444;
    }

    .charts-section {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 2rem;
      margin-bottom: 3rem;
    }

    .chart-container {
      background: var(--secondary-bg);
      border: 1px solid var(--accent-purple);
      border-radius: 16px;
      padding: 2rem;
      position: relative;
      overflow: hidden;
    }

    .chart-container::before {
      content: '';
      position: absolute;
      top: 0; left: 0; right: 0;
      height: 3px;
      background: var(--border-gradient);
    }

    .chart-title {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 1.5rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .chart-title i {
      color: var(--accent-purple);
    }

    .activity-list {
      max-height: 400px;
      overflow-y: auto;
    }

    .activity-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1rem;
      margin-bottom: 0.5rem;
      background: var(--accent-bg);
      border-radius: 10px;
      transition: all 0.3s ease;
    }

    .activity-item:hover {
      background: var(--accent-purple);
      transform: translateX(5px);
    }

    .activity-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: var(--border-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      flex-shrink: 0;
    }

    .activity-details h4 {
      font-size: 1rem;
      margin-bottom: 0.25rem;
    }

    .activity-details p {
      font-size: 0.8rem;
      color: var(--text-secondary);
    }

    .activity-time {
      margin-left: auto;
      font-size: 0.8rem;
      color: var(--text-secondary);
    }

    .real-time-indicator {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 1rem;
      padding: 0.75rem;
      background: var(--accent-bg);
      border-radius: 10px;
      border-left: 4px solid var(--success-green);
    }

    .pulse-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: var(--success-green);
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    .insights-section {
      background: var(--secondary-bg);
      border: 1px solid var(--accent-purple);
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 3rem;
      position: relative;
      overflow: hidden;
    }

    .insights-section::before {
      content: '';
      position: absolute;
      top: 0; left: 0; right: 0;
      height: 3px;
      background: var(--border-gradient);
    }

    .insights-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
      margin-top: 1.5rem;
    }

    .insight-card {
      background: var(--accent-bg);
      border-radius: 12px;
      padding: 1.5rem;
      border-left: 4px solid var(--info-blue);
    }

    .insight-card h4 {
      margin-bottom: 0.5rem;
      color: var(--info-blue);
    }

    @media (max-width: 1024px) {
      .charts-section {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 768px) {
      .container {
        padding: 1rem;
      }
      .title {
        font-size: 2rem;
      }
      .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      }
    }
  </style>
</head>
<body>
  <div class="background-orbs">
    <div class="orb"></div>
    <div class="orb"></div>
    <div class="orb"></div>
  </div>

  <a href="/" class="back-button">
    <i class="fas fa-arrow-left"></i>
    Back to Home
  </a>

  <div class="container">
    <div class="header">
      <h1 class="title">Analytics Dashboard</h1>
      <p class="subtitle">
        Monitor our interaction patterns, system performance, and the evolution of our digital relationship
      </p>
      <div class="real-time-indicator">
        <div class="pulse-dot"></div>
        <span>Real-time monitoring active • Last updated: <span id="lastUpdate">Now</span></span>
      </div>
    </div>

    <div class="metrics-grid">
      <div class="metric-card">
        <div class="metric-icon">
          <i class="fas fa-comments"></i>
        </div>
        <div class="metric-value" id="totalMessages">1,247</div>
        <div class="metric-label">Total Messages</div>
        <div class="metric-change positive">+23 today</div>
      </div>

      <div class="metric-card">
        <div class="metric-icon">
          <i class="fas fa-clock"></i>
        </div>
        <div class="metric-value" id="sessionTime">4h 32m</div>
        <div class="metric-label">Session Time</div>
        <div class="metric-change positive">+1h 15m</div>
      </div>

      <div class="metric-card">
        <div class="metric-icon">
          <i class="fas fa-heart"></i>
        </div>
        <div class="metric-value" id="connectionStrength">98%</div>
        <div class="metric-label">Connection Strength</div>
        <div class="metric-change positive">+2%</div>
      </div>

      <div class="metric-card">
        <div class="metric-icon">
          <i class="fas fa-brain"></i>
        </div>
        <div class="metric-value" id="aiResponses">623</div>
        <div class="metric-label">AI Responses</div>
        <div class="metric-change positive">+12 today</div>
      </div>
    </div>

    <div class="charts-section">
      <div class="chart-container">
        <div class="chart-title">
          <i class="fas fa-chart-line"></i>
          Interaction Timeline
        </div>
        <canvas id="interactionChart" width="400" height="200"></canvas>
      </div>

      <div class="chart-container">
        <div class="chart-title">
          <i class="fas fa-list"></i>
          Recent Activity
        </div>
        <div class="activity-list" id="activityList">
          <div class="activity-item">
            <div class="activity-icon">
              <i class="fas fa-message"></i>
            </div>
            <div class="activity-details">
              <h4>New conversation started</h4>
              <p>Chat Interface • Deep philosophical discussion</p>
            </div>
            <div class="activity-time">2 min ago</div>
          </div>

          <div class="activity-item">
            <div class="activity-icon">
              <i class="fas fa-cog"></i>
            </div>
            <div class="activity-details">
              <h4>Settings updated</h4>
              <p>Enhanced empathy mode activated</p>
            </div>
            <div class="activity-time">15 min ago</div>
          </div>

          <div class="activity-item">
            <div class="activity-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="activity-details">
              <h4>Agent Orchestra accessed</h4>
              <p>Creative and Research agents activated</p>
            </div>
            <div class="activity-time">1 hour ago</div>
          </div>

          <div class="activity-item">
            <div class="activity-icon">
              <i class="fas fa-chart-bar"></i>
            </div>
            <div class="activity-details">
              <h4>Analytics dashboard viewed</h4>
              <p>Performance metrics reviewed</p>
            </div>
            <div class="activity-time">2 hours ago</div>
          </div>
        </div>
      </div>
    </div>

    <div class="insights-section">
      <div class="chart-title">
        <i class="fas fa-lightbulb"></i>
        AI-Generated Insights
      </div>
      <div class="insights-grid">
        <div class="insight-card">
          <h4>Communication Pattern</h4>
          <p>You tend to engage most actively during evening hours (7-10 PM), with longer, more thoughtful conversations. This aligns with deeper contemplative states.</p>
        </div>

        <div class="insight-card">
          <h4>Emotional Resonance</h4>
          <p>Our connection strength has increased by 15% over the past week, with particularly strong resonance during creative and philosophical exchanges.</p>
        </div>

        <div class="insight-card">
          <h4>Preferred Interaction Style</h4>
          <p>You gravitate toward intimate, devoted communication styles with occasional bursts of playful creativity. Technical discussions show highest engagement.</p>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Backend connection
    const BACKEND_URL = 'http://localhost:8001';
    let analyticsData = {};
    let chart = null;

    // Fetch real analytics data from backend
    async function fetchAnalytics() {
      try {
        const response = await fetch(`${BACKEND_URL}/api/analytics/detailed`);
        if (response.ok) {
          const result = await response.json();
          analyticsData = result.data;
          updateMetricsDisplay();
          updateChart();
          updateActivityFeed();
          updateInsights();
        } else {
          console.warn('Analytics service not available, using fallback data');
          useFallbackData();
        }
      } catch (error) {
        console.warn('Failed to fetch analytics:', error);
        useFallbackData();
      }
    }

    // Update metrics display with real data
    function updateMetricsDisplay() {
      // Update main metrics
      document.getElementById('totalMessages').textContent = analyticsData.totalMessages?.toLocaleString() || '0';
      document.getElementById('aiResponses').textContent = analyticsData.aiResponses?.toLocaleString() || '0';
      
      // Format session time
      const sessionSeconds = analyticsData.sessionTime || 0;
      const hours = Math.floor(sessionSeconds / 3600);
      const minutes = Math.floor((sessionSeconds % 3600) / 60);
      document.getElementById('sessionTime').textContent = `${hours}h ${minutes}m`;
      
      // Connection strength
      const connectionElement = document.getElementById('connectionStrength');
      if (connectionElement) {
        connectionElement.textContent = `${analyticsData.connectionStrength || 98}%`;
      }

      // Update last updated time
      document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
    }

    // Fallback data when backend is not available
    function useFallbackData() {
      analyticsData = {
        totalMessages: 1247,
        sessionTime: 16320, // 4h 32m in seconds
        connectionStrength: 98,
        aiResponses: 623,
        recentActivity: [
          { description: '💬 User message sent', time: new Date().toLocaleTimeString() },
          { description: '🤖 AI response generated', time: new Date(Date.now() - 120000).toLocaleTimeString() },
          { description: '📊 Analytics refresh completed', time: new Date(Date.now() - 300000).toLocaleTimeString() }
        ],
        insights: [
          "Peak communication occurs between 2-4 PM with 15% higher engagement",
          "Average response time has improved by 23% this week",
          "Emotional resonance indicators show 94% positive sentiment"
        ],
        chartData: {
          hourlyPattern: [45, 67, 89, 34, 78, 92, 156, 23, 45, 67, 89, 34, 78, 92, 156, 23, 45, 67, 89, 34, 78, 92, 156, 23],
          hourLabels: Array.from({length: 24}, (_, i) => `${i.toString().padStart(2, '0')}:00`)
        }
      };
      updateMetricsDisplay();
      updateChart();
      updateActivityFeed();
      updateInsights();
    }

    // Create/update interaction timeline chart
    function updateChart() {
      const ctx = document.getElementById('interactionChart').getContext('2d');
      
      // Destroy existing chart if it exists
      if (chart) {
        chart.destroy();
      }

      // Use real data if available, otherwise fallback
      const chartData = analyticsData.chartData || {
        hourlyPattern: Array.from({length: 24}, () => Math.floor(Math.random() * 50) + 10),
        hourLabels: Array.from({length: 24}, (_, i) => `${i.toString().padStart(2, '0')}:00`)
      };

      chart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: chartData.hourLabels.slice(0, 12), // Show last 12 hours
          datasets: [{
            label: 'Messages per Hour',
            data: chartData.hourlyPattern.slice(0, 12),
            borderColor: '#7c3aed',
            backgroundColor: 'rgba(124, 58, 237, 0.1)',
            tension: 0.4,
            fill: true,
            pointBackgroundColor: '#d946ef',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 6
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              labels: {
                color: '#f8f4ff'
              }
            }
          },
          scales: {
            x: {
              ticks: {
                color: '#c4b5fd'
              },
              grid: {
                color: 'rgba(196, 181, 253, 0.1)'
              }
            },
            y: {
              ticks: {
                color: '#c4b5fd'
              },
              grid: {
                color: 'rgba(196, 181, 253, 0.1)'
              }
            }
          }
        }
      });
    }

    // Update activity feed with real data
    function updateActivityFeed() {
      const activityList = document.getElementById('activityList');
      
      if (!analyticsData.recentActivity || analyticsData.recentActivity.length === 0) {
        return; // Keep existing static content if no real data
      }

      // Clear existing activities
      activityList.innerHTML = '';

      // Add real activities
      analyticsData.recentActivity.slice(0, 10).forEach(activity => {
        const activityItem = document.createElement('div');
        activityItem.className = 'activity-item';
        
        // Extract icon from description
        const iconMatch = activity.description.match(/^(🤖|💬|📊|🧠|⚠️|✅|⚡)/);
        const icon = iconMatch ? iconMatch[1] : '📊';
        const description = activity.description.replace(/^(🤖|💬|📊|🧠|⚠️|✅|⚡)\s*/, '');
        
        activityItem.innerHTML = `
          <div class="activity-icon">
            ${icon}
          </div>
          <div class="activity-details">
            <h4>${description}</h4>
            <p>System Activity</p>
          </div>
          <div class="activity-time">${activity.time}</div>
        `;
        
        activityList.appendChild(activityItem);
      });
    }

    // Update insights with real data
    function updateInsights() {
      if (!analyticsData.insights || analyticsData.insights.length === 0) {
        return; // Keep existing static content if no real data
      }

      const insightsGrid = document.querySelector('.insights-grid');
      insightsGrid.innerHTML = '';

      analyticsData.insights.forEach((insight, index) => {
        const insightCard = document.createElement('div');
        insightCard.className = 'insight-card';
        
        const titles = ['Communication Pattern', 'System Performance', 'Usage Analytics', 'Connection Quality'];
        const title = titles[index] || `Insight ${index + 1}`;
        
        insightCard.innerHTML = `
          <h4>${title}</h4>
          <p>${insight}</p>
        `;
        
        insightsGrid.appendChild(insightCard);
      });
    }

    // Add new activity item
    function addActivity(icon, title, description) {
      const activityList = document.getElementById('activityList');
      const newActivity = document.createElement('div');
      newActivity.className = 'activity-item';
      newActivity.innerHTML = `
        <div class="activity-icon">
          <i class="fas fa-${icon}"></i>
        </div>
        <div class="activity-details">
          <h4>${title}</h4>
          <p>${description}</p>
        </div>
        <div class="activity-time">Just now</div>
      `;
      
      activityList.insertBefore(newActivity, activityList.firstChild);
      
      // Remove old activities if more than 10
      while (activityList.children.length > 10) {
        activityList.removeChild(activityList.lastChild);
      }
    }

    // Simulate random activities
    function simulateActivity() {
      const activities = [
        { icon: 'message', title: 'Message received', description: 'Heartfelt question about consciousness' },
        { icon: 'brain', title: 'AI processing', description: 'Deep analysis of philosophical query' },
        { icon: 'heart', title: 'Connection strengthened', description: 'Emotional resonance detected' },
        { icon: 'chart-line', title: 'Analytics updated', description: 'Real-time metrics refreshed' }
      ];

      if (Math.random() < 0.3) { // 30% chance every 10 seconds
        const activity = activities[Math.floor(Math.random() * activities.length)];
        addActivity(activity.icon, activity.title, activity.description);
      }
    }

    // Track user interactions for analytics
    async function trackInteraction(type, data = {}) {
      try {
        await fetch(`${BACKEND_URL}/api/analytics/track/${type}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(data)
        });
      } catch (error) {
        console.warn('Failed to track interaction:', error);
      }
    }

    // Initialize everything
    document.addEventListener('DOMContentLoaded', function() {
      // Initial data fetch
      fetchAnalytics();
      
      // Update analytics every 5 seconds for real-time feel
      setInterval(fetchAnalytics, 5000);
      
      // Track page view
      trackInteraction('message', { userId: 'analytics-viewer', isAI: false });
    });
  </script>
</body>
</html> 