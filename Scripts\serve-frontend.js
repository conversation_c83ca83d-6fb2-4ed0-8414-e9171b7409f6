import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const app = express();
const PORT = 3000;

// Serve static files from current directory (excluding frontend folder to avoid conflicts)
app.use(express.static('.', {
  ignore: ['frontend/**']
}));

// Handle frontend routing
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

// Serve chat interface (our enhanced version)
app.get('/chat', (req, res) => {
  res.sendFile(path.join(__dirname, 'chat.html'));
});

// Serve analytics dashboard
app.get('/analytics', (req, res) => {
  res.sendFile(path.join(__dirname, 'analytics.html'));
});

// Serve agents page
app.get('/agents', (req, res) => {
  res.sendFile(path.join(__dirname, 'agents.html'));
});

// Serve models page
app.get('/models', (req, res) => {
  res.sendFile(path.join(__dirname, 'models.html'));
});

// Serve settings page
app.get('/settings', (req, res) => {
  res.sendFile(path.join(__dirname, 'settings.html'));
});

// Serve playground page
app.get('/playground', (req, res) => {
  res.sendFile(path.join(__dirname, 'playground.html'));
});

// Serve any other HTML files from root
app.get('*.html', (req, res) => {
  res.sendFile(path.join(__dirname, req.path));
});

function startServer(port = PORT) {
  return app.listen(port, () => {
    if (process.env.NODE_ENV !== 'test') {
      console.log(`🌟 Yara's Frontend Server is alive on http://localhost:${port}`);
      console.log(`💫 Navigation Hub: http://localhost:${port}`);
      console.log(`💬 Chat Interface: http://localhost:${port}/chat`);
      console.log(`🔗 Backend API: http://localhost:8001`);
    }
  });
}

// Start the server if the script is run directly
if (process.argv[1] === fileURLToPath(import.meta.url)) {
  startServer();
}

export { app, startServer }; 